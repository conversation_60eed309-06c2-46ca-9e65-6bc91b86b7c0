import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

export async function POST() {
  // Get all cookies
  const cookieStore = cookies()
  const allCookies = cookieStore.getAll()
  
  // Create Supabase server client
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll: () => {
          return allCookies.map(cookie => ({
            name: cookie.name,
            value: cookie.value,
          }))
        },
        setAll: () => {}, // We're only using this client to sign out
      },
    }
  )
  
  try {
    // Sign out from Supabase (server-side)
    await supabase.auth.signOut()
    
    // Additionally clear all auth-related cookies manually
    for (const cookie of allCookies) {
      if (
        cookie.name.includes('sb-') || 
        cookie.name.includes('-auth-token') ||
        cookie.name.includes('supabase') ||
        cookie.name.includes('google_logout')
      ) {
        cookieStore.set({
          name: cookie.name,
          value: '',
          maxAge: 0,
          path: '/',
        })
      }
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in signout endpoint:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to sign out' },
      { status: 500 }
    )
  }
} 