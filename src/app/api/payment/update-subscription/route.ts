import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';

// Helper function to verify authentication
async function verifyAuth() {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          // Not needed for this context
        },
        remove(name: string, options: any) {
          // Not needed for this context
        },
      },
    }
  );

  const { data: userData, error: userError } = await supabase.auth.getUser();
  if (userError || !userData?.user) {
    return { authenticated: false, userId: null, clientId: null };
  }

  // Get client_id from clients table
  const { data: clientData, error: clientError } = await supabase
    .from('clients')
    .select('client_id')
    .eq('auth_id', userData.user.id)
    .single();

  if (clientError || !clientData) {
    return { authenticated: true, userId: userData.user.id, clientId: null };
  }

  return { 
    authenticated: true, 
    userId: userData.user.id, 
    clientId: clientData.client_id 
  };
}

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, userId, clientId } = await verifyAuth();
    if (!authenticated || !userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { tran_id, status, custom_fields } = body;

    // Validate required parameters
    if (!tran_id || !status) {
      return NextResponse.json(
        { error: 'Missing required parameters: tran_id, status' },
        { status: 400 }
      );
    }

    // Log the request
    console.log('Update subscription request:', {
      tran_id,
      status,
      custom_fields,
      clientId
    });

    // Check if payment was successful
    if (status !== 'success' && status !== '00') {
      return NextResponse.json(
        { error: 'Payment was not successful', status },
        { status: 400 }
      );
    }

    // Decode custom fields if provided
    let decodedCustomFields: any = {};
    if (custom_fields) {
      try {
        const decodedString = Buffer.from(custom_fields, 'base64').toString('utf-8');
        decodedCustomFields = JSON.parse(decodedString);
      } catch (error) {
        console.error('Error decoding custom fields:', error);
        // Continue with default values
      }
    }

    // Extract plan information
    const plan_type = decodedCustomFields.plan_type || 'assistant';
    const billing_cycle = decodedCustomFields.billing_cycle || 1;

    // Calculate next billing date based on billing cycle
    const now = new Date();
    let nextBillingDate = new Date();
    
    if (billing_cycle === 0) {
      // 1-month plan
      nextBillingDate.setMonth(now.getMonth() + 1);
    } else if (billing_cycle === 1) {
      // 3-month plan
      nextBillingDate.setMonth(now.getMonth() + 3);
    } else {
      // Default to 1 month
      nextBillingDate.setMonth(now.getMonth() + 1);
    }
    
    // Format next billing date as ISO string
    const next_billing_date = nextBillingDate.toISOString().split('T')[0];

    // Create Supabase admin client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Update client subscription in database
    const { data, error } = await supabase
      .from('clients')
      .update({
        plan_type,
        next_billing_date,
        subscription_status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('client_id', clientId);
    
    if (error) {
      console.error('Error updating client subscription:', error);
      return NextResponse.json(
        { error: 'Failed to update subscription' },
        { status: 500 }
      );
    }
    
    // Log payment transaction
    const { data: transactionData, error: transactionError } = await supabase
      .from('payment_transactions')
      .insert({
        client_id: clientId,
        transaction_id: tran_id,
        amount: decodedCustomFields.amount || '0.00',
        currency: 'USD',
        status: status,
        status_message: 'Payment successful',
        approval_code: 'N/A',
        payment_method: 'payway',
        plan_type,
        billing_cycle
      });
    
    if (transactionError) {
      console.error('Error logging payment transaction:', transactionError);
      // Continue despite transaction logging error
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Subscription updated successfully'
    });
    
  } catch (error: any) {
    console.error('Error in update subscription API:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update subscription' },
      { status: 500 }
    );
  }
}
