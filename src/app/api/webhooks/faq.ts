import { NextResponse } from 'next/server';

/**
 * Send a webhook notification for a new FAQ
 * @param faqData Object containing FAQ information
 * @returns Promise that resolves when the webhook is sent (fire and forget)
 */
export async function sendFaqWebhook(faqData: {
  faq_id: string;
  client_id: string;
  question: string;
  answer: string;
  lang?: string;
  sector?: string | null;
  audioUrl?: string | null;
}) {
  try {
    // Call our internal API route instead of the external webhook directly
    fetch('/api/webhooks/faq', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(faqData),
    }).catch(error => {
      // Log any errors but don't throw - this is fire and forget
      console.error('Error sending FAQ webhook:', error);
    });

  } catch (error) {
    console.error('Error initiating FAQ webhook:', error);
  }
}

/**
 * Send a webhook notification for an updated FAQ
 * @param faqData Object containing updated FAQ information
 * @returns Promise that resolves when the webhook is sent (fire and forget)
 */
export async function sendFaqUpdateWebhook(faqData: {
  faq_id: string;
  client_id: string;
  answer: string;
  lang?: string;
  sector?: string | null;
  audio_url?: string;
  is_photo?: boolean;
}) {
  try {
    // Call our internal API route instead of the external webhook directly
    fetch('/api/webhooks/faq/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(faqData),
    }).catch(error => {
      // Log any errors but don't throw - this is fire and forget
      console.error('Error sending FAQ update webhook:', error);
    });

  } catch (error) {
    console.error('Error initiating FAQ update webhook:', error);
  }
}
