import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    // Get the connection webhook URL from environment variables
    const connectionWebhookUrl = process.env.CONNECTION_WEBHOOK_URL;

    if (!connectionWebhookUrl) {
      console.error('CONNECTION_WEBHOOK_URL environment variable not configured');
      return NextResponse.json({ error: 'Connection service endpoint not configured.' }, { status: 500 });
    }

    // Parse the request body
    const payload = await request.json();
    const { client_id, webhook_url, token, type } = payload;

    // Validate required fields
    if (!client_id || !token || !type) {
      console.error('API route received incomplete data:', { client_id: client_id ? 'Present' : 'Missing', token: token ? 'Present' : 'Missing', type: type ? 'Present' : 'Missing' });
      return NextResponse.json({ error: 'Missing required fields for platform connection.' }, { status: 400 });
    }

    console.log(`Forwarding connection request to webhook for client: ${client_id}, platform: ${type}`);

    // Forward the request to the actual webhook
    const response = await fetch(connectionWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id,
        webhook_url,
        token,
        type
      }),
    });

    // Check if webhook reported success (status 2xx)
    if (!response.ok) {
      const errorBody = await response.text(); // Get error details if available
      console.error(`Connection webhook failed with status ${response.status}:`, errorBody);

      // Return the error to the client with the appropriate status code
      return NextResponse.json(
        { error: `Platform connection service failed (Status: ${response.status})` },
        { status: response.status }
      );
    }

    console.log(`Successfully connected platform ${type} for client: ${client_id}`);

    // Return success to the client
    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Error in connection webhook API route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process connection request.' },
      { status: 500 }
    );
  }
}
