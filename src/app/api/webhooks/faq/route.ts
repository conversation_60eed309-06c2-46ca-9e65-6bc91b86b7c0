import { NextResponse } from 'next/server';

// This is a server-side API route
export async function POST(request: Request) {
  try {
    const faqData = await request.json();

    // Get the webhook URL based on language (these env vars are server-side only)
    let webhookUrl;

    if (faqData.lang === 'kh') {
      webhookUrl = process.env.FAQ_KH_WEBHOOK_URL;
    } else if (faqData.lang === 'en') {
      webhookUrl = process.env.FAQ_EN_WEBHOOK_URL;
    } else {
      // Fallback to default webhook URL
      webhookUrl = process.env.FAQ_WEBHOOK_URL || process.env.FAQ_EN_WEBHOOK_URL;
    }

    if (!webhookUrl) {
      console.warn(`Webhook URL not defined for language: ${faqData.lang || 'default'}`);
      return NextResponse.json({ error: 'Webhook URL not configured' }, { status: 500 });
    }

    // Forward the request to the actual webhook
    await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(faqData),
    });

    // Return success
    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error sending FAQ webhook:', error);
    return NextResponse.json({ error: 'Failed to send webhook' }, { status: 500 });
  }
}