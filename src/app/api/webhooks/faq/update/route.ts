import { NextResponse } from 'next/server';

// This is a server-side API route for FAQ updates
export async function POST(request: Request) {
  try {
    const faqData = await request.json();

    // Get the webhook URL based on language (these env vars are server-side only)
    let webhookUrl;

    if (faqData.lang === 'kh') {
      webhookUrl = process.env.FAQ_UPDATE_KH_WEBHOOK_URL;
    } else if (faqData.lang === 'en') {
      webhookUrl = process.env.FAQ_UPDATE_EN_WEBHOOK_URL;
    } else {
      // Fallback to default webhook URL
      webhookUrl = process.env.FAQ_UPDATE_WEBHOOK_URL || process.env.FAQ_UPDATE_EN_WEBHOOK_URL;
    }

    if (!webhookUrl) {
      console.warn(`Update webhook URL not defined for language: ${faqData.lang || 'default'}`);
      return NextResponse.json({ error: 'Update webhook URL not configured' }, { status: 500 });
    }

    // Forward the request to the actual webhook
    await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(faqData),
    });

    // Return success
    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error sending FAQ update webhook:', error);
    return NextResponse.json({ error: 'Failed to send update webhook' }, { status: 500 });
  }
}
