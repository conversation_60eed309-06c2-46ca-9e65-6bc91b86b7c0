import { NextResponse } from 'next/server';

// This is a server-side API route for welcome message updates
export async function POST(request: Request) {
  try {
    const welcomeData = await request.json();

    // Get the webhook URL based on language (these env vars are server-side only)
    let webhookUrl;

    if (welcomeData.lang === 'kh') {
      webhookUrl = process.env.WELCOME_KH_WEBHOOK_URL;
    } else if (welcomeData.lang === 'en') {
      webhookUrl = process.env.WELCOME_EN_WEBHOOK_URL;
    } else {
      // Fallback to default webhook URL
      webhookUrl = process.env.WELCOME_WEBHOOK_URL || process.env.WELCOME_EN_WEBHOOK_URL;
    }

    if (!webhookUrl) {
      console.warn(`Welcome webhook URL not defined for language: ${welcomeData.lang || 'default'}`);
      return NextResponse.json({ error: 'Welcome webhook URL not configured' }, { status: 500 });
    }

    // Forward the request to the actual webhook
    await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(welcomeData),
    });

    // Return success
    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error sending welcome update webhook:', error);
    return NextResponse.json({ error: 'Failed to send welcome webhook' }, { status: 500 });
  }
}
