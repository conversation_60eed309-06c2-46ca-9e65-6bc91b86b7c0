import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getRedisValue, setRedisValue } from '@/utils/redis/client';

export async function POST(request: Request) {
  try {
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';
    
    // Create rate limit key with IP
    const rateLimitKey = `ratelimit:username-check:${ip}`;
    
    // Check rate limit (60 requests per minute)
    const requestCount = await getRedisValue<number>(rateLimitKey) || 0;
    if (requestCount >= 60) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }
    
    // Increment request count with 60-second expiry
    await setRedisValue(rateLimitKey, Number(requestCount) + 1, 60);
    
    // Parse request body
    const body = await request.json();
    const { username } = body;

    // Validate required parameters
    if (!username) {
      return NextResponse.json(
        { error: 'Missing required parameter: username' },
        { status: 400 }
      );
    }

    // Create Supabase admin client with service role key
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    try {
      // Check if the username exists in the clients table using admin client
      const { data: clientData, error: clientError } = await adminClient
        .from('clients')
        .select('username')
        .ilike('username', username) // Case-insensitive comparison
        .limit(1);

      if (clientError) {
        console.error('Error checking username in clients table:', clientError);
      } else if (clientData && clientData.length > 0) {
        // Username exists in clients table
        return NextResponse.json({
          exists: true,
        });
      }

      // If we reach here, the username doesn't exist in the clients table
      return NextResponse.json({
        exists: false,
      });
    } catch (error) {
      console.error('Error checking username:', error);

      // If all else fails, assume username is available
      return NextResponse.json({
        exists: false,
        warning: "Could not verify username uniqueness. Proceed with caution."
      });
    }
  } catch (error) {
    console.error('Error in check-username API:', error);
    return NextResponse.json(
      { error: 'Failed to check username' },
      { status: 500 }
    );
  }
}
