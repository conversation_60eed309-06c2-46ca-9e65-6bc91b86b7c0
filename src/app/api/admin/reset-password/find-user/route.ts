import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export async function POST(request: Request) {
  try {
    // First verify the requester is admin
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // This won't be used in this context
          },
          remove(name: string, options: any) {
            // This won't be used in this context
          },
        },
      }
    )
    
    // Verify admin user
    const { data: userData, error: userError } = await supabase.auth.getUser()
    if (userError || !userData?.user || userData.user.email !== '<EMAIL>') {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 403 }
      )
    }
    
    // Now that we've verified admin status, use the service role client
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )
    
    // Get the email from the request
    const { email } = await request.json()
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' }, 
        { status: 400 }
      )
    }
    
    // Extract username from email
    const username = email.split('@')[0]
    
    // Try to find user in the clients table by username
    try {
      const { data: clientUser, error: clientError } = await adminClient
        .from('clients')
        .select('auth_id')
        .eq('username', username)
        .single()
      
      if (clientError || !clientUser) {
        return NextResponse.json(
          { error: `No user found with username: ${username}` }, 
          { status: 404 }
        )
      }
      
      // Return only the client user ID
      return NextResponse.json({
        userId: clientUser.auth_id
      })
    } catch (lookupError) {
      console.error('Error during user lookup:', lookupError)
      return NextResponse.json(
        { error: 'Error finding user' }, 
        { status: 500 }
      )
    }
    
  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    )
  }
} 