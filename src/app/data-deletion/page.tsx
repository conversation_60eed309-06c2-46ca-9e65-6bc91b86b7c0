'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { useState } from 'react'

export default function DataDeletion() {
  const [email, setEmail] = useState('')
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      setError('Email is required')
      return
    }

    // In a real implementation, you would send this request to your backend
    // For now, we'll just simulate a successful submission
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setSubmitted(true)
      setError('')
    } catch (err) {
      setError('An error occurred. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Logo Header */}
      <div className="px-4 py-8 mx-auto flex items-center justify-center">
        <Link href="/">
          <img
            src="/images/white_tran_logo.svg"
            alt="ChhlatBot"
            className="h-10 w-auto"
          />
        </Link>
      </div>

      <motion.div
        className="flex-grow container mx-auto px-4 py-12 text-gray-300"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-3xl mx-auto p-8 rounded-xl border border-white/20 shadow-xl"
          style={{
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.30)'
          }}>
          <h1 className="text-3xl font-bold mb-8 text-white">Data Deletion Request</h1>

          <div className="prose prose-invert prose-zinc max-w-none">
            {!submitted ? (
              <>
                <p className="mb-6">You have the right to request deletion of your personal data collected and processed by ChhlatBot. This page explains how to submit a data deletion request.</p>

                <h2 className="text-xl font-semibold mt-8 mb-4 text-white">How to Request Data Deletion</h2>

                <p className="mb-4">You can request deletion of your data in the following ways:</p>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Option 1: Email Request</h3>
                <p>Send an email to <a href="mailto:<EMAIL>" className="text-jade-purple hover:underline"><EMAIL></a> with the subject line "Data Deletion Request".</p>
                <p>Please include the following information:</p>
                <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
                  <li>The email address associated with your ChhlatBot account</li>
                  <li>Your full name</li>
                  <li>A statement requesting deletion of your data</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Option 2: Account Settings</h3>
                <p>If you have an active account, you can delete your account and associated data by:</p>
                <ol className="list-decimal pl-6 mt-2 mb-4 space-y-2">
                  <li>Logging into your ChhlatBot account</li>
                  <li>Navigating to Account Settings</li>
                  <li>Selecting "Delete Account" at the bottom of the page</li>
                </ol>

                <h3 className="text-lg font-semibold mt-6 mb-3 text-white">Option 3: Facebook Data</h3>
                <p>For data collected through Facebook integration:</p>
                <ol className="list-decimal pl-6 mt-2 mb-4 space-y-2">
                  <li>Go to your Facebook account settings</li>
                  <li>Navigate to "Apps and Websites"</li>
                  <li>Find ChhlatBot in your connected apps</li>
                  <li>Remove the app to revoke permissions</li>
                </ol>
                <p>In addition, you can submit a deletion request using the form below:</p>

                <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2">
                      Your Email Address*
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full px-4 py-3 bg-zinc-800 border border-zinc-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple"
                      placeholder="Enter your email address"
                    />
                  </div>

                  {error && (
                    <div className="p-3 bg-red-900/30 border border-red-800 rounded-lg text-red-200 text-sm">
                      {error}
                    </div>
                  )}

                  <button
                    type="submit"
                    className="w-full py-3 px-6 text-center bg-jade-purple text-white font-medium rounded-lg hover:bg-opacity-90 transition-all duration-200"
                  >
                    Submit Deletion Request
                  </button>
                </form>
              </>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold mb-4 text-white">Request Received</h2>
                <p className="mb-6">We've received your data deletion request and will process it within 30 days.</p>
                <p className="mb-6">A confirmation email has been sent to {email}.</p>
                <p>If you have any questions about your request, please contact us at <a href="mailto:<EMAIL>" className="text-jade-purple hover:underline"><EMAIL></a>.</p>
              </div>
            )}

            <h2 className="text-xl font-semibold mt-8 mb-4 text-white">What Happens Next</h2>
            <p>After submitting your request, we will:</p>
            <ol className="list-decimal pl-6 mt-2 mb-6 space-y-2">
              <li>Verify your identity to protect your privacy</li>
              <li>Process your request within 30 days</li>
              <li>Send a confirmation email when your data has been deleted</li>
            </ol>

            <p>Please note that some information may be retained for legal, tax, or regulatory reasons even after your deletion request. This data will be deleted once these requirements are satisfied.</p>

            <h2 className="text-xl font-semibold mt-8 mb-4 text-white">Data Retention</h2>
            <p>When you delete your account or request data deletion, we will remove your personal information from our active systems. However, we may retain certain information for the following reasons:</p>
            <ul className="list-disc pl-6 mt-2 mb-4 space-y-2">
              <li>To comply with legal obligations</li>
              <li>To resolve disputes</li>
              <li>To enforce our terms and agreements</li>
              <li>To protect against fraudulent or illegal activity</li>
            </ul>

            <p className="mt-8 text-sm text-gray-500">For more information about our data practices, please refer to our <Link href="/privacy" className="text-jade-purple hover:underline">Privacy Policy</Link>.</p>
          </div>
        </div>
      </motion.div>

      <Footer />
    </div>
  )
}