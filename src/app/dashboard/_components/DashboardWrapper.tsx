'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@/utils/supabase/client'
import { getClientInfo, setClientInfo, clearClientInfo, ClientInfo } from '@/utils/client'

export default function DashboardWrapper({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()
  const [clientInfo, setClientInfoState] = useState<ClientInfo | null>(null)

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Ensure client info is available and user is authenticated
  useEffect(() => {
    if (!isClient) return

    async function ensureClientInfo() {
      setIsLoading(true)
      try {
        // Fetch user
        const { data: authData, error: authError } = await supabase.auth.getUser()

        if (authError || !authData.user) {
          // Handle auth error or missing user
          clearClientInfo()
          router.push('/access')
          return
        }

        // Always fetch fresh client info from the database
        const { data: clientData, error: clientError } = await supabase
          .from('clients')
          .select('client_id, username, sector, lang, plan_type')
          .eq('auth_id', authData.user.id)
          .single()

        if (clientError) {
          console.error('Error fetching client data:', clientError)
          setError(`Unable to access your profile: ${clientError.message}`)
          clearClientInfo()
        } else if (clientData) {
          // Prepare the client info object with fresh data
          const newClientInfo: ClientInfo = {
            client_id: clientData.client_id,
            username: clientData.username,
            subscription_tier: clientData.plan_type || null,
            sector: clientData.sector,
            lang: clientData.lang
          }
          // Store updated client info in session storage
          setClientInfo(newClientInfo)
          setClientInfoState(newClientInfo)
        }
      } catch (error: any) {
        console.error('Error ensuring client info:', error)
        setError(`An unexpected error occurred: ${error.message}`)
        clearClientInfo()
      } finally {
        setIsLoading(false)
      }
    }

    ensureClientInfo()
  }, [isClient, supabase, router])

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      // Sign out of Supabase (locally)
      const { error } = await supabase.auth.signOut()
      if (error) {
        throw error
      }

      // Clear cookies server-side
      await fetch('/api/auth/signout', {
        method: 'POST',
        cache: 'no-store'
      })

      // Clear client info from session storage
      if (isClient) {
        clearClientInfo()
      }

      // Redirect to access page using router
      router.push('/access')

      // For a complete reset of client state, refresh the page after navigation
      // but only if we're in the browser
      if (isClient) {
        setTimeout(() => {
          router.refresh()
        }, 100)
      }
    } catch (error) {
      console.error('Error signing out:', error)
      // If we encounter an error, still redirect to access page
      router.push('/access')
    } finally {
      setIsSigningOut(false) // Ensure signing out state is reset
    }
  }

  if (isLoading && isClient) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="w-8 h-8 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black px-4">
        <div className="max-w-md w-full bg-zinc-900 p-6 rounded-lg border border-zinc-800">
          <h2 className="text-xl text-red-400 font-bold mb-4">Access Error</h2>
          <p className="text-zinc-300 mb-6">{error}</p>
          <div className="flex justify-between">
            <button
              onClick={handleSignOut}
              disabled={isSigningOut}
              className="px-4 py-2 bg-zinc-800 text-white rounded hover:bg-zinc-700 transition-colors disabled:opacity-50"
            >
              {isSigningOut ? 'Signing Out...' : 'Sign Out'}
            </button>
            <button
              onClick={() => router.refresh()}
              className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="dashboard-wrapper">
      {children}
    </div>
  )
}
