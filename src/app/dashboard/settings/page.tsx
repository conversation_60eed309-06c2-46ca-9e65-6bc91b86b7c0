'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { FaGlobe } from 'react-icons/fa'
import { getClientInfo, setClientInfo, ClientInfo } from '@/utils/client'
import { motion } from 'framer-motion'

export default function SettingsPage() {
  const [oldPassword, setOldPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [clientInfo, setClientInfoState] = useState<ClientInfo | null>(null)
  const [currentLang, setCurrentLang] = useState<'en' | 'kh'>('en')
  const [showLangConfirm, setShowLangConfirm] = useState(false)
  const [showSecondConfirm, setShowSecondConfirm] = useState(false)
  const [newLang, setNewLang] = useState<'en' | 'kh'>('en')
  const [isChangingLang, setIsChangingLang] = useState(false)
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false)

  const router = useRouter()
  const supabase = createClientComponentClient()

  // Load client info on component mount
  useEffect(() => {
    const info = getClientInfo()
    setClientInfoState(info)
    if (info?.lang) {
      setCurrentLang(info.lang as 'en' | 'kh')
    }
  }, [])

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccess('')
    setIsLoading(true)

    if (newPassword !== confirmPassword) {
      setError('New passwords do not match')
      setIsLoading(false)
      return
    }

    try {
      // Get the current user's email first
      const { data: userData } = await supabase.auth.getUser()
      const userEmail = userData?.user?.email

      if (!userEmail) {
        throw new Error('Unable to verify user email')
      }

      // First sign in with the old password to verify it
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: oldPassword
      })

      if (signInError) {
        throw new Error('Current password is incorrect')
      }

      // Then update to the new password
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) throw error

      setSuccess('Password updated successfully')
      setOldPassword('')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error: any) {
      setError(error.message || 'Failed to update password')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      const { error } = await supabase.auth.signOut()
      if (error) throw error

      await fetch('/api/auth/signout', {
        method: 'POST',
        cache: 'no-store'
      })

      router.push('/access')

      setTimeout(() => {
        router.refresh()
      }, 100)
    } catch (error) {
      console.error('Error signing out:', error)
      router.push('/')
    }
  }

  const confirmSignOut = () => {
    setShowLogoutConfirmation(true)
  }

  // Handle language change
  const handleLanguageChange = async (lang: 'en' | 'kh') => {
    setNewLang(lang)
    setShowLangConfirm(true)
  }

  // Handle first confirmation
  const handleFirstConfirm = () => {
    setShowLangConfirm(false)
    setShowSecondConfirm(true)
  }

  // Handle second confirmation and update language
  const handleSecondConfirm = async () => {
    if (!clientInfo) return

    try {
      setIsChangingLang(true)

      // Create a new client info object with the updated language
      const updatedClientInfo: ClientInfo = {
        ...clientInfo,
        lang: newLang
      }

      // Update the client info in session storage
      setClientInfo(updatedClientInfo)

      // Update the state
      setClientInfoState(updatedClientInfo)
      setCurrentLang(newLang)

      // Update the language in the clients table
      const { error: clientsError } = await supabase
        .from('clients')
        .update({ lang: newLang })
        .eq('client_id', clientInfo.client_id)

      if (clientsError) {
        console.error('Error updating language in clients table:', clientsError)
        setError('Failed to update language setting. Please try again.')
        return
      }

      // Update the language in the configs table
      const { error: configsError } = await supabase
        .from('configs')
        .update({ lang: newLang })
        .eq('client_id', clientInfo.client_id)

      if (configsError) {
        console.error('Error updating language in configs table:', configsError)
        setError('Failed to update language setting in configs. Please try again.')
      } else {
        // Create a success message that appears in the language section
        const languageSection = document.querySelector('.language-settings-section')
        if (languageSection) {
          const successDiv = document.createElement('div')
          successDiv.className = 'text-green-500 text-sm font-body mt-4 text-center'
          successDiv.textContent = 'ChhlatBot language updated successfully'
          languageSection.appendChild(successDiv)

          // Remove the success message after 3 seconds
          setTimeout(() => {
            if (successDiv.parentNode === languageSection) {
              languageSection.removeChild(successDiv)
            }
          }, 3000)
        }
      }
    } catch (error) {
      console.error('Error changing language:', error)
      setError('Failed to update language setting. Please try again.')
    } finally {
      setIsChangingLang(false)
      setShowSecondConfirm(false)
    }
  }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      <div className="flex-grow container mx-auto px-4 pt-4 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Top navigation-like row with logo */}
          <div className="flex justify-center py-2 mb-8">
            <Link href="/dashboard">
              <img
                src="/images/white_tran_logo.svg"
                alt="Chhlat Logo"
                className="h-10 w-auto cursor-pointer hover:opacity-80 transition-opacity"
              />
            </Link>
          </div>

          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <Link href="/dashboard" className="text-sm text-zinc-400 hover:text-white">
                ← Back
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                Settings
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          <div className="max-w-md mx-auto">
            <div className="backdrop-blur-sm border rounded-xl p-6 mb-8" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.10), inset 0 0 10px rgba(255, 255, 255, 0.10)'
            }}>
              <h2 className="text-xl font-bold mb-6 font-title text-white">Change Password</h2>

            <form onSubmit={handlePasswordChange} className="space-y-4">
              <div>
                <label htmlFor="oldPassword" className="block text-sm font-medium text-zinc-400 font-body mb-1">
                  Current Password
                </label>
                <input
                  type="password"
                  id="oldPassword"
                  value={oldPassword}
                  onChange={(e) => setOldPassword(e.target.value)}
                  required
                  className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white font-body focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50"
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                />
              </div>

              <div>
                <label htmlFor="newPassword" className="block text-sm font-medium text-zinc-400 font-body mb-1">
                  New Password
                </label>
                <input
                  type="password"
                  id="newPassword"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white font-body focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50"
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                />
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-zinc-400 font-body mb-1">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white font-body focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50"
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                />
              </div>

              {error && (
                <div className="text-red-500 text-sm font-body">{error}</div>
              )}

              {success && (
                <div className="text-green-500 text-sm font-body">{success}</div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors font-body text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                }}
              >
                {isLoading ? 'Updating...' : 'Update Password'}
              </button>
            </form>
          </div>

          {/* Language Settings Section */}
          <div className="backdrop-blur-sm border rounded-xl p-6 mb-8 language-settings-section" style={{
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 0 10px rgba(255, 255, 255, 0.10), inset 0 0 10px rgba(255, 255, 255, 0.10)'
          }}>
            <h2 className="text-xl font-bold mb-4 font-title text-white">ChhlatBot Language</h2>
            <p className="text-zinc-400 text-sm mb-6 font-body">
              This setting defines your ChhlatBot's main language. All your questions, answers, and audio recordings should be in this language.
            </p>

            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentLang === 'en' ? 'bg-jade-purple text-white' : 'bg-white/10 text-zinc-400'}`}>
                    <FaGlobe size={16} />
                  </div>
                  <span className="font-body">English</span>
                </div>
                <button
                  onClick={() => handleLanguageChange('en')}
                  disabled={currentLang === 'en' || isChangingLang}
                  className={`px-3 py-2 rounded-lg text-sm font-body transition-colors ${
                    currentLang === 'en'
                      ? 'bg-jade-purple-dark/80 text-white cursor-default'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {currentLang === 'en' ? 'Active' : 'Select'}
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentLang === 'kh' ? 'bg-jade-purple text-white' : 'bg-white/10 text-zinc-400'}`}>
                    <span className="text-xs font-semibold">ខ្មែរ</span>
                  </div>
                  <span className="font-body">Khmer</span>
                </div>
                <button
                  onClick={() => handleLanguageChange('kh')}
                  disabled={currentLang === 'kh' || isChangingLang}
                  className={`px-3 py-2 rounded-lg text-sm font-body transition-colors ${
                    currentLang === 'kh'
                      ? 'bg-jade-purple-dark/80 text-white cursor-default'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {currentLang === 'kh' ? 'Active' : 'Select'}
                </button>
              </div>
            </div>
          </div>

          {/* Account Section */}
          <div className="backdrop-blur-sm rounded-xl p-6" style={{
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 0 10px rgba(255, 255, 255, 0.10), inset 0 0 10px rgba(255, 255, 255, 0.10)'
          }}>
            <h2 className="text-xl font-bold mb-6 font-title text-white">Account</h2>

            <button
              onClick={confirmSignOut}
              disabled={isSigningOut}
              className="w-full py-3 px-4 border border-red-600 bg-transparent text-red-500 hover:bg-red-900/20 rounded-lg transition-colors font-body text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                boxShadow: '0 0 10px rgba(255, 59, 48, 0.2)'
              }}
            >
              {isSigningOut ? 'Logging out...' : 'Log out'}
            </button>
          </div>

          {/* First Language Change Confirmation Modal */}
          {showLangConfirm && (
            <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50">
              <div className="bg-jade-purple/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4" style={{
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  Change ChhlatBot Language?
                </h3>
                <p className="text-zinc-300 mb-6 text-center">
                  You are about to change your ChhlatBot's main language to <span className="text-jade-purple font-semibold">{newLang === 'en' ? 'English' : 'Khmer'}</span>.
                  <br />
                  This affects how your ChhlatBot processes questions and answers. You should provide all content in {newLang === 'en' ? 'English' : 'Khmer'} after this change.
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowLangConfirm(false)}
                    className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                   style={{
                     boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                   }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleFirstConfirm}
                    className="flex-1 py-2 bg-jade-purple text-white hover:bg-jade-purple-dark/75 hover:shadow-md hover:bg-jade-purple hover:border-jade-purple transition-all duration-200 rounded-lg font-medium border border-white/20"
                    style={{
                      boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    Continue
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Second Language Change Confirmation Modal */}
          {showSecondConfirm && (
            <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50">
              <div className="bg-jade-purple/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4" style={{
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  Final Confirmation
                </h3>
                <div className="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-4 mb-6">
                  <p className="text-yellow-300 font-body text-sm">
                    <strong>Important:</strong> By changing to {newLang === 'en' ? 'English' : 'Khmer'}, you confirm that:
                  </p>
                  <ul className="list-disc pl-5 mt-2 text-yellow-200 text-sm font-body space-y-1">
                    <li>You will provide all questions and answers in {newLang === 'en' ? 'English' : 'Khmer'}</li>
                    <li>All audio recordings should be in {newLang === 'en' ? 'English' : 'Khmer'}</li>
                    <li>Your ChhlatBot will be optimized for {newLang === 'en' ? 'English' : 'Khmer'} conversations</li>
                    <li>The system will translate between languages when needed</li>
                  </ul>
                </div>
                <p className="text-zinc-300 mb-6 text-center">
                  Are you absolutely sure you want to change your ChhlatBot's language?
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowSecondConfirm(false)}
                    className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                   style={{
                     boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                   }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSecondConfirm}
                    disabled={isChangingLang}
                    className="flex-1 py-2 bg-jade-purple text-white hover:bg-jade-purple-dark/75 hover:shadow-md hover:bg-jade-purple hover:border-jade-purple transition-all duration-200 rounded-lg font-medium border border-white/20"
                    style={{
                      boxShadow: '0 0 10px rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    {isChangingLang ? 'Updating...' : 'Yes, Change'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Logout Confirmation Modal */}
          {showLogoutConfirmation && (
            <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50">
              <div className="bg-red-500/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4" style={{
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  Confirm Logout
                </h3>
                <p className="text-zinc-300 mb-6 text-center">
                  Are you sure you want to log out of your account?
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowLogoutConfirmation(false)}
                    className="flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSignOut}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"
                    style={{
                      boxShadow: '0 0 15px rgba(239, 68, 68, 0.3)'
                    }}
                  >
                    Yes, Log Out
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        </motion.div>
      </div>
    </div>
  )
}
