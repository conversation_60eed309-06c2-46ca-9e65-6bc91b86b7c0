'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { FiSettings } from 'react-icons/fi'
import { FaPaperPlane, FaGlobe } from 'react-icons/fa'
import { getClientInfo, setClientInfo, ClientInfo } from '@/utils/client'
import { useLanguage } from '@/context/LanguageContext'
import LanguageSwitcher from '@/components/LanguageSwitcher'

export default function Dashboard() {
  const [user, setUser] = useState<any>(null)
  // const [isLoading, setIsLoading] = useState(true)
  // const [isSigningOut, setIsSigningOut] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [clientInfo, setClientInfoState] = useState<ClientInfo | null>(null)
  const [subscriptionData, setSubscriptionData] = useState<{
    plan_type: string | null;
    next_billing_date: string | null;
  }>({
    plan_type: null,
    next_billing_date: null
  })
  const [usageData, setUsageData] = useState<{
    usage_used: number;
    usage_limit: number;
  }>({
    usage_used: 0,
    usage_limit: 2000
  })
  const router = useRouter()
  const supabase = createClientComponentClient()
  const { t, language } = useLanguage()
  const [isDataLoading, setIsDataLoading] = useState(true)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Effect to sync UI language with bot language when UI language changes
  useEffect(() => {
    if (clientInfo && language) {
      // Only update if the languages are different
      if (clientInfo.lang !== language) {
        updateBotLanguage(language);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [language])

  // We'll handle loading in the data loading effect

  useEffect(() => {
    if (!isMounted) return

    async function loadDashboardData() {
      setIsDataLoading(true)
      try {
        const { data: authData, error: authError } = await supabase.auth.getUser()

        if (authError || !authData.user) {
          router.push('/access')
          return
        }

        setUser(authData.user)

        const cachedClientInfo = getClientInfo()
        setClientInfoState(cachedClientInfo)

        if (cachedClientInfo) {
          // Get plan_type and next_billing_date directly from clients table
          const { data: clientData, error: clientError } = await supabase
            .from('clients')
            .select('plan_type, next_billing_date, lang')
            .eq('client_id', cachedClientInfo.client_id)
            .single()

          if (clientError) {
            console.error('Error fetching client data:', clientError)
            setSubscriptionData({ plan_type: null, next_billing_date: null })
          } else if (clientData) {
            setSubscriptionData({
              plan_type: language === 'kh' ?
                (clientData.plan_type === 'Intern' ? 'អ្នកហាត់ការ' :
                 clientData.plan_type === 'Assistant' ? 'ជំនួយការ' :
                 clientData.plan_type) : clientData.plan_type,
              next_billing_date: clientData.next_billing_date
            })

            // Update the cached client info with the latest language from the database
            if (clientData.lang) {
              const updatedClientInfo = {
                ...cachedClientInfo,
                lang: clientData.lang as 'en' | 'kh'
              };
              setClientInfo(updatedClientInfo);
              setClientInfoState(updatedClientInfo);
            }
          }

          // Get usage data from configs table
          const { data: configData, error: configError } = await supabase
            .from('configs')
            .select('usage_used, usage_limit')
            .eq('client_id', cachedClientInfo.client_id)
            .single()

          if (configError) {
            console.error('Error fetching usage data:', configError)
            // Set default values if there's an error
            setUsageData({ usage_used: 0, usage_limit: 2000 })
          } else if (configData) {
            setUsageData({
              usage_used: configData.usage_used || 0,
              usage_limit: configData.usage_limit || 2000
            })
          } else {
            // Set default values if no data is found
            setUsageData({ usage_used: 0, usage_limit: 2000 })
          }
        } else {
          setSubscriptionData({ plan_type: null, next_billing_date: null })
          setUsageData({ usage_used: 0, usage_limit: 2000 })
        }
      } catch (error) {
        console.error('Error loading dashboard data:', error)
      } finally {
        setIsDataLoading(false)
      }
    }

    loadDashboardData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMounted])

  // Format the date to a more readable format
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not available';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // This function updates the database settings for the bot's language
  // This is separate from the UI language which is handled by LanguageContext
  const updateBotLanguage = async (lang: 'en' | 'kh') => {
    if (!clientInfo || clientInfo.lang === lang) return;

    try {
      // Create a new client info object with the updated language
      const updatedClientInfo: ClientInfo = {
        ...clientInfo,
        lang
      };

      // Update the client info in session storage
      setClientInfo(updatedClientInfo);

      // Update the state
      setClientInfoState(updatedClientInfo);

      // Update the language in the clients table
      const { error: clientsError } = await supabase
        .from('clients')
        .update({ lang })
        .eq('client_id', clientInfo.client_id);

      if (clientsError) {
        console.error('Error updating language in clients table:', clientsError);
      }

      // Update the language in the configs table
      const { error: configsError } = await supabase
        .from('configs')
        .update({ lang })
        .eq('client_id', clientInfo.client_id);

      if (configsError) {
        console.error('Error updating language in configs table:', configsError);
      }
    } catch (error) {
      console.error('Error changing bot language:', error);
    }
  }

  // Unused sign out function - handled by DashboardWrapper
  // const handleSignOut = async () => {
  //   try {
  //     setIsSigningOut(true)
  //     const { error } = await supabase.auth.signOut()
  //     if (error) throw error

  //     // Only use window in client-side environment
  //     if (typeof window !== 'undefined') {
  //       window.location.href = '/'
  //     }
  //   } catch (error) {
  //     console.error('Error signing out:', error)
  //   } finally {
  //     setIsSigningOut(false)
  //   }
  // }

  // We don't need this anymore since we're using the global loading overlay
  // if (isDataLoading) {
  //   return (
  //     <div className="min-h-screen bg-deep-blue flex items-center justify-center">
  //       <div className="text-center">
  //         <div className="w-16 h-16 border-4 border-jade-purple border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
  //         <p className="text-gray-300 font-body">Loading...</p>
  //       </div>
  //     </div>
  //   )
  // }

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>
      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
            style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
          >
            {/* Subtle background gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-30 rounded-2xl"></div>

            {/* Content */}
            <div className="relative z-10 flex justify-between items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>

              <div className="flex items-center space-x-2 sm:space-x-3">
                {/* User Email - Desktop with icon */}
                <div className="hidden md:flex items-center space-x-2">
                  <div className="w-7 h-7 bg-jade-purple/20 rounded-full flex items-center justify-center">
                    <svg className="w-3.5 h-3.5 text-jade-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div className="text-sm text-zinc-300 font-body">
                    {user?.email?.split('@')[0]}
                  </div>
                </div>

                {/* User Email - Mobile/Tablet without icon */}
                <div className="flex md:hidden text-xs text-zinc-300 font-body">
                  {user?.email?.split('@')[0]}
                </div>

                {/* Language Switcher */}
                <div>
                  <LanguageSwitcher />
                </div>

                {/* Settings Button */}
                <Link
                  href="/dashboard/settings"
                  className="w-8 h-8 sm:w-9 sm:h-9 bg-white/10 border border-white/20 rounded-lg flex items-center justify-center text-zinc-300 hover:text-white hover:bg-jade-purple/20 hover:border-jade-purple/40 transition-all duration-300 group"
                  style={{
                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                  }}
                >
                  <FiSettings size={16} className="transition-transform duration-300 group-hover:rotate-90" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Dashboard Content */}
      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* <h1 className="text-2xl md:text-3xl font-bold mb-8 font-title" dangerouslySetInnerHTML={{ __html: t('welcome_dashboard') }}>
          </h1> */}

          {!clientInfo && !isDataLoading && (
            <div className="bg-amber-500/10 backdrop-blur-md border border-white/20 rounded-xl p-4 mb-8 text-amber-200 shadow-lg shadow-white/10">
              <h2 className="text-xl font-semibold mb-2">{t('account_setup_incomplete')}</h2>
              <p className="mb-4">{t('account_setup_message')}</p>
              <p>{t('email')} {user?.email}</p>
              <p className="text-sm mt-2">{t('contact_support')}</p>
            </div>
          )}

          {/* Modern Dashboard Overview - Combined Messages and Plan */}
          <div className="mb-6">
            {/* Combined Messages and Plan Card */}
            <div className="mb-6">
              {/* Combined Messages & Plan Card */}
              <div
                className="relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
                style={{
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                <div className="relative z-10">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-white font-title">{t('dashboard_header')}</h2>
                    <a
                      href="https://t.me"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-gradient-to-r from-jade-purple to-purple-600 text-white rounded-xl font-body text-sm hover:from-jade-purple/80 hover:to-purple-600/80 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      {t('upgrade')}
                    </a>
                  </div>

                  {/* Content Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-2 gap-20">
                    {/* Messages Section */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-jade-purple/20 rounded-xl flex items-center justify-center">
                          <FaPaperPlane className="text-jade-purple" size={18} />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white font-title">{t('messages')}</h3>
                          {/* <p className="text-xs text-zinc-400 font-body">{t('usage')} {t('progress')}</p> */}
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex justify-between items-end">
                          <div>
                            <div className="text-2xl font-bold text-white font-title">
                              {usageData.usage_used.toLocaleString()}
                            </div>
                            <div className="text-sm text-zinc-400 font-body">
                              of {usageData.usage_limit.toLocaleString()} {t('total')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-semibold text-jade-purple font-title">
                              {Math.round((usageData.usage_used / usageData.usage_limit) * 100)}%
                            </div>
                            <div className="text-xs text-zinc-400 font-body">used</div>
                          </div>
                        </div>

                        <div className="h-3 bg-zinc-800/50 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-gradient-to-r from-jade-purple via-purple-500 to-blue-500 rounded-full transition-all duration-500"
                            style={{
                              width: `${Math.min((usageData.usage_used / usageData.usage_limit) * 100, 100)}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* Plan Section */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-emerald-500/20 rounded-xl flex items-center justify-center">
                          <svg className="w-5 h-5 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white font-title">{t('plan')}</h3>
                          {/* <p className="text-xs text-zinc-400 font-body">{t('current')} subscription</p> */}
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <div className="text-2xl font-bold text-jade-purple font-title py-3">
                            {subscriptionData.plan_type || 'N/A'}
                          </div>
                          <div className="text-sm text-zinc-400 font-body">
                            {t('bill')} {formatDate(subscriptionData.next_billing_date)}
                          </div>
                        </div>

                        {/* <Link
                          href="/dashboard/plans"
                          className="inline-flex items-center space-x-2 text-sm text-jade-purple hover:text-white transition-colors font-body"
                        >
                          <span>Manage Plan</span>
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Secondary Cards Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* AI Brain Card */}
            <Link
              href="/dashboard/knowledge"
              className="relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center">
                      <svg className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white font-title">{t('ai_brain')}</h3>
                      {/* <p className="text-xs text-zinc-400 font-body">{t('knowledge')} {t('base')}</p> */}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-sm text-zinc-300 font-body">{t('train')} {t('your')} {t('ai')} {t('assistant')}</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="text-xs bg-white/10 text-zinc-300 px-3 py-1.5 rounded-full font-body border border-white/20 flex items-center space-x-1">
                      <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                      </svg>
                      <span>{t('text')}</span>
                    </span>
                    <span className="text-xs bg-white/10 text-zinc-300 px-3 py-1.5 rounded-full font-body border border-white/20 flex items-center space-x-1">
                      <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                      <span>{t('voice')}</span>
                    </span>
                    <span className="text-xs bg-white/10 text-zinc-300 px-3 py-1.5 rounded-full font-body border border-white/20 flex items-center space-x-1">
                      <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span>{t('image')}</span>
                    </span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Connections Card */}
            <Link
              href="/dashboard/connect"
              className="relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-500/20 rounded-xl flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white font-title">{t('connects')}</h3>
                      {/* <p className="text-xs text-zinc-400 font-body">{t('social')} {t('platforms')}</p> */}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <p className="text-sm text-zinc-300 font-body">{t('connect')} {t('your')} {t('social')} {t('media')}</p>
                  <div className="flex flex-wrap gap-3">
                    {/* Facebook Messenger */}
                    <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center text-blue-400 border border-blue-500/30">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.477 2 2 6.145 2 11.259C2 14.128 3.134 16.71 5 18.552V22.5L8.801 20.509C9.795 20.83 10.871 21 12 21C17.523 21 22 16.855 22 11.741C22 6.627 17.523 2.482 12 2ZM13.162 14.841L10.4 11.841L5 14.841L10.8 8.759L13.6 11.759L19 8.759L13.162 14.841Z" fill="currentColor"/>
                      </svg>
                    </div>
                    {/* Instagram */}
                    <div className="w-10 h-10 bg-pink-500/20 rounded-xl flex items-center justify-center text-pink-400 border border-pink-500/30">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="2" y="2" width="20" height="20" rx="5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M18 6L18 6.01" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    {/* Telegram */}
                    <div className="w-10 h-10 bg-blue-400/20 rounded-xl flex items-center justify-center text-blue-300 border border-blue-400/30">
                      <FaPaperPlane size={16} />
                    </div>
                    {/* Web */}
                    <div className="w-10 h-10 bg-purple-500/20 rounded-xl flex items-center justify-center text-purple-400 border border-purple-500/30">
                      <FaGlobe size={16} />
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* Get Started Section */}
          {/* <div className="relative bg-deep-blue/60 backdrop-blur-lg border border-white/50 rounded-xl p-6 mb-8 animate-pulse-slow" style={{
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.25)',
            animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'
          }}>
            <div className="absolute inset-0 rounded-xl overflow-hidden" style={{
              background: 'radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 70%)',
              pointerEvents: 'none'
            }}></div>
            <div className="absolute inset-0 rounded-xl overflow-hidden opacity-30" style={{
              background: 'linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%)',
              pointerEvents: 'none'
            }}></div>
            <h2 className="text-xl font-bold mb-4 font-title">Getting Started with ChhlatBot</h2>
            <div className="space-y-4">
              <div className="flex">
                <div className="flex-shrink-0 w-8 h-8 bg-white/25 rounded-full flex items-center justify-center mr-4" style={{
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.15), inset 0 0 5px rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}>
                  <span className="text-white font-bold font-title">1</span>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 font-title">Connect Your Social Media Pages</h3>
                  <p className="text-zinc-400 text-sm font-body">Link your Facebook Messenger, Instagram, WhatsApp, and Telegram.</p>
                </div>
              </div>

              <div className="flex">
                <div className="flex-shrink-0 w-8 h-8 bg-white/25 rounded-full flex items-center justify-center mr-4" style={{
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.15), inset 0 0 5px rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}>
                  <span className="text-white font-bold font-title">2</span>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 font-title">Customize Your Business Insights</h3>
                  <p className="text-zinc-400 text-sm font-body">Provide your business informations to train the AI to match your brand voice and answer common questions. (Can be text, voice, and image)</p>
                </div>
              </div>

              <div className="flex">
                <div className="flex-shrink-0 w-8 h-8 bg-white/25 rounded-full flex items-center justify-center mr-4" style={{
                  boxShadow: '0 0 10px rgba(255, 255, 255, 0.15), inset 0 0 5px rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}>
                  <span className="text-white font-bold font-title">3</span>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 font-title">Monitor Performance</h3>
                  <p className="text-zinc-400 text-sm font-body">Track how your AI is handling customer inquiries and make improvements.</p>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <button className="relative bg-white/10 backdrop-blur-lg border border-white/40 rounded-xl px-6 py-3 text-white font-body hover:bg-white/15 hover:border-white/60 transition-all duration-300" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.05)'
              }}>
                Start Setup Guide
              </button>
            </div>
          </div> */}





          {/* <div className="relative bg-deep-blue/70 backdrop-blur-lg rounded-xl p-6 mb-8" style={{
            // boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.3)'
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            border: '1px solid rgba(255, 255, 255, 0.25)',
            borderRadius: '0.75rem',
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.20)'
          }}>

            <div className="absolute inset-0 rounded-xl overflow-hidden opacity-30" style={{
              // background: 'linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%)',
              pointerEvents: 'none'
            }}></div>
            <h2 className="text-xl font-bold mb-4 font-title">Message History</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Channel</th>
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Amount</th>
                    <th className="text-left py-3 px-4 font-semibold text-zinc-400 font-body">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-white/20">
                    <td className="py-3 px-4 font-body">Apr 10, 2025</td>
                    <td className="py-3 px-4 font-body">Facebook</td>
                    <td className="py-3 px-4 font-body">500</td>
                    <td className="py-3 px-4 font-body"><span className="text-green-500">Delivered</span></td>
                  </tr>
                  <tr className="border-b border-white/20">
                    <td className="py-3 px-4 font-body">Apr 5, 2025</td>
                    <td className="py-3 px-4 font-body">Instagram</td>
                    <td className="py-3 px-4 font-body">300</td>
                    <td className="py-3 px-4 font-body"><span className="text-green-500">Delivered</span></td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 font-body">Apr 1, 2025</td>
                    <td className="py-3 px-4 font-body">All Channels</td>
                    <td className="py-3 px-4 font-body">1,200</td>
                    <td className="py-3 px-4 font-body"><span className="text-green-500">Delivered</span></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div> */}

          {/* Recent Activity (Empty State) */}
          {/* <div className="relative bg-deep-blue/70 backdrop-blur-lg rounded-xl p-6" style={{
            // boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.3)'
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            border: '1px solid rgba(255, 255, 255, 0.25)',
            borderRadius: '0.75rem',
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.20)'
          }}>
            <div className="absolute inset-0 rounded-xl overflow-hidden opacity-30" style={{
              // background: 'linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%)',
              pointerEvents: 'none'
            }}></div>
            <h2 className="text-xl font-bold mb-4 font-title">Recent Activity</h2>
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-white/10 rounded-full flex items-center justify-center shadow-lg shadow-white/10">
                <svg className="w-8 h-8 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2 font-title">No activity yet</h3>
              <p className="text-zinc-500 text-sm max-w-md mx-auto font-body">
                Once you connect your accounts and start receiving messages, you'll see your activity here.
              </p>
            </div>
          </div> */}
        </motion.div>
      </div>

      <Footer />
    </div>
  )
}
