'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { useState, useEffect, useRef } from 'react'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useAuth } from '@/context/AuthContext'
import { getClientInfo } from '@/utils/client'
import Footer from '@/components/Footer'
import { FaPaperPlane, FaFacebookMessenger, FaInstagram, /* FaWhatsapp, */ FaGlobe, FaTrash } from 'react-icons/fa'
import { deleteRedisValue } from '@/utils/redis/clientApi'
import { useLanguage } from '@/context/LanguageContext'

type WebhookState = {
  [key: string]: {
    isCopied: boolean;
  };
};

type EditState = {
  [key: string]: {
    isEditing: boolean;
  };
};

// Define platform type for documentation purposes
// 'facebook' | 'instagram' | 'whatsapp' | 'telegram' | 'web' | 'tiktok';

// Define client credentials type
type ClientCredentials = {
  id: number;
  client_id: string;
  fb_url?: string;
  ig_url?: string;
  /* wa_url?: string; */
  web_url?: string;
  web_domain?: string;
  fb_token?: string;
  ig_token?: string;
  /* wa_token?: string; */
  tg_token?: string;
  created_at?: string;
  updated_at?: string;
};

export default function EditConnectionPage() {
  const { user: _ } = useAuth() // Unused but kept for context
  const supabase = createClientComponentClient()
  const { t } = useLanguage()

  // State variables
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [credentials, setCredentials] = useState<ClientCredentials | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null)
  // Connection limit is fetched but not used in edit page since we're only editing existing connections
  const [webhookStates, setWebhookStates] = useState<WebhookState>({
    'fb-webhook': { isCopied: false },
    'ig-webhook': { isCopied: false },
    /* 'wa-webhook': { isCopied: false }, */
    'web-webhook': { isCopied: false }
  })
  const [editStates, setEditStates] = useState<EditState>({
    'facebook': { isEditing: false },
    'instagram': { isEditing: false },
    /* 'whatsapp': { isEditing: false }, */
    'telegram': { isEditing: false },
    'web': { isEditing: false }
  })
  const [showConfirmation, setShowConfirmation] = useState<{show: boolean, platform: string | null}>({
    show: false,
    platform: null
  })

  // State for delete confirmation
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<{show: boolean, platform: string | null}>({
    show: false,
    platform: null
  })

  // State for tracking if deletion is in progress
  const [isDeleting, setIsDeleting] = useState(false)

  // Track connected platforms
  const [connectedPlatforms, setConnectedPlatforms] = useState<{[key: string]: boolean}>({
    facebook: false,
    instagram: false,
    /* whatsapp: false, */
    telegram: false,
    web: false
  })

  // Store token values (used to track token changes)
  const [tokenValues, setTokenValues] = useState<{[key: string]: string}>({
    facebook: '',
    instagram: '',
    /* whatsapp: '', */
    telegram: ''
  })

  // Refs for input fields
  const facebookTokenRef = useRef<HTMLInputElement>(null)
  const instagramTokenRef = useRef<HTMLInputElement>(null)
  /* const whatsappTokenRef = useRef<HTMLInputElement>(null) */
  const telegramTokenRef = useRef<HTMLInputElement>(null)
  const webDomainRef = useRef<HTMLInputElement>(null)

  // Ref for delete confirmation modal
  const deleteConfirmModalRef = useRef<HTMLDivElement>(null)

  // Set isClient to true when component mounts and fetch credentials
  useEffect(() => {
    setIsClient(true)
    fetchClientCredentials()
    fetchSubscriptionLimits()
  }, [])

  // Close delete confirmation modal when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (deleteConfirmModalRef.current && !deleteConfirmModalRef.current.contains(event.target as Node) && !isDeleting) {
        setShowDeleteConfirmation({ show: false, platform: null })
      }
    }

    if (showDeleteConfirmation.show) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDeleteConfirmation.show, isDeleting])

  // Fetch subscription limits from plans table
  // Note: In the edit page, we don't need to use the connection limit since we're only editing existing connections
  const fetchSubscriptionLimits = async () => {
    try {
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;
      const subscriptionTier = clientInfo?.subscription_tier;

      if (!clientId) {
        console.error('Client ID not found while fetching subscription limits');
        return;
      }

      if (!subscriptionTier) {
        console.error('No subscription tier found for client');
        return;
      }

      // Get all plans and filter in JavaScript instead of using eq()
      const { data: allPlans, error: planError } = await supabase
        .from('plans')
        .select('name, connections');

      if (planError) {
        console.error('Error fetching plans data:', planError);
        return;
      }

      // Find the matching plan with case-insensitive comparison
      const planData = allPlans?.find(plan =>
        plan.name.trim().toLowerCase() === subscriptionTier.trim().toLowerCase()
      );

      if (!planData) {
        console.error('No matching plan found for:', subscriptionTier);
        return;
      }

      // Connection limit is not used in edit page
      // We keep this function for consistency with the connect page
    } catch (error) {
      console.error('Error in fetchSubscriptionLimits:', error);
    }
  };

  // Fetch client credentials from database
  const fetchClientCredentials = async () => {
    try {
      setIsLoading(true)
      const clientInfo = getClientInfo()
      const clientId = clientInfo?.client_id

      if (!clientId) {
        setErrorMessage('Client ID not found. Please ensure you are properly logged in.')
        return
      }

      // Query the client_credentials table
      const { data, error } = await supabase
        .from('client_credentials')
        .select('*')
        .eq('client_id', clientId)
        .maybeSingle()

      if (error) {
        console.error('Error fetching client credentials:', error)
        setErrorMessage(`Failed to load connection data: ${error.message}`)
        return
      }

      // If no record exists, redirect to connect page
      if (!data) {
        window.location.href = '/dashboard/connect';
        return;
      }

      setCredentials(data)

      // Update token values
      const newTokenValues = {
        facebook: data.fb_token || '',
        instagram: data.ig_token || '',
        /* whatsapp: data.wa_token || '', */
        telegram: data.tg_token || ''
      }
      setTokenValues(newTokenValues)

      // Set connected platforms based on token/domain presence
      const newConnectedPlatforms = {
        facebook: !!data.fb_token,
        instagram: !!data.ig_token,
        /* whatsapp: !!data.wa_token, */
        telegram: !!data.tg_token,
        web: !!data.web_domain
      }
      setConnectedPlatforms(newConnectedPlatforms)

      // Populate input fields with token values
      if (facebookTokenRef.current && data.fb_token) {
        facebookTokenRef.current.value = data.fb_token
      }
      if (instagramTokenRef.current && data.ig_token) {
        instagramTokenRef.current.value = data.ig_token
      }
      /* if (whatsappTokenRef.current && data.wa_token) {
        whatsappTokenRef.current.value = data.wa_token
      } */
      if (telegramTokenRef.current && data.tg_token) {
        telegramTokenRef.current.value = data.tg_token
      }
      if (webDomainRef.current && data.web_domain) {
        webDomainRef.current.value = data.web_domain
      }
    } catch (error) {
      console.error('Unexpected error in fetchClientCredentials:', error)
      setErrorMessage('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }

  // This function gets the webhook URL without exposing it in the DOM
  const getWebhookUrl = (id: string): string => {
    // Get the appropriate URL based on the webhook ID
    if (id === 'fb-webhook' && credentials?.fb_url) {
      return credentials.fb_url;
    } else if (id === 'ig-webhook' && credentials?.ig_url) {
      return credentials.ig_url;
    /* } else if (id === 'wa-webhook' && credentials?.wa_url) {
      return credentials.wa_url; */
    } else if (id === 'web-webhook' && credentials?.web_url) {
      return credentials.web_url;
    } else {
      // Fallback to default URLs if credentials are not available
      if (id === 'fb-webhook') return 'https://api.chhlatbot.com/webhook/messenger';
      else if (id === 'ig-webhook') return 'https://api.chhlatbot.com/webhook/instagram';
      /* else if (id === 'wa-webhook') return 'https://api.chhlatbot.com/webhook/whatsapp'; */
      else if (id === 'web-webhook') return 'https://api.chhlatbot.com/webhook/web';
    }
    return '';
  };

  const handleCopyWebhook = async (id: string) => {
    try {
      // Get the URL directly from the function, not storing it in a variable in the component
      const url = getWebhookUrl(id);

      // Copy to clipboard
      await navigator.clipboard.writeText(url);

      // Show success state (just the checkmark icon)
      setWebhookStates(prev => ({
        ...prev,
        [id]: { isCopied: true }
      }));

      // Reset copy status after 1.5 seconds
      setTimeout(() => {
        setWebhookStates(prev => ({
          ...prev,
          [id]: { isCopied: false }
        }));
      }, 1500);
    } catch (err) {
      console.error('Failed to copy:', err);
      setErrorMessage('Failed to copy webhook URL to clipboard');

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    }
  };

  // We've removed the toggleWebhookVisibility function as it's no longer needed

  // Toggle edit mode for a platform
  const toggleEditMode = (platform: string) => {
    setEditStates(prev => ({
      ...prev,
      [platform]: {
        isEditing: !prev[platform].isEditing
      }
    }));
  };

  // We've removed the handleTokenChange function as it's no longer needed

  // Show delete confirmation dialog
  const initiateDelete = (platform: string) => {
    setShowDeleteConfirmation({
      show: true,
      platform
    });
  };

  // Handle platform deletion after confirmation
  const handleDelete = async (platform: string) => {
    try {
      // Set deleting state
      setIsDeleting(true);

      // Get client ID
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        setErrorMessage('Client ID not found. Please ensure you are properly logged in.');
        setIsDeleting(false);
        return;
      }

      // Determine which fields to update based on platform
      let updateFields = {};
      let platformPrefix = '';
      switch (platform) {
        case 'facebook':
          updateFields = { fb_token: '', fb_status: 0 };
          platformPrefix = 'fb';
          break;
        case 'instagram':
          updateFields = { ig_token: '', ig_status: 0 };
          platformPrefix = 'ig';
          break;
        /* case 'whatsapp':
          updateFields = { wa_token: '', wa_status: 0 };
          platformPrefix = 'wa';
          break; */
        case 'telegram':
          updateFields = { tg_token: '', tg_status: 0 };
          platformPrefix = 'tg';
          break;
        case 'web':
          updateFields = { web_domain: '', web_status: 0 };
          platformPrefix = 'web';
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      // Special handling for Telegram - delete webhook before removing token
      if (platform === 'telegram' && telegramTokenRef.current?.value) {
        try {
          const token = telegramTokenRef.current.value;
          // Call Telegram API to delete the webhook
          const telegramResponse = await fetch(`https://api.telegram.org/bot${token}/deleteWebhook`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            }
            // No body needed for deleteWebhook
          });

          const telegramData = await telegramResponse.json();

          if (!telegramData.ok) {
            console.warn('Warning: Failed to delete Telegram webhook:', telegramData.description);
            // Continue with deletion even if webhook deletion fails
          } else {
            console.log('Successfully deleted Telegram webhook');
          }
        } catch (telegramError) {
          console.warn('Error deleting Telegram webhook:', telegramError);
          // Continue with deletion even if webhook deletion fails
        }
      }

      // Update the database to set the token/domain to empty and status to 0
      const { error: updateError } = await supabase
        .from('client_credentials')
        .update(updateFields)
        .eq('client_id', clientId);

      if (updateError) {
        console.error(`Error deleting ${platform} connection:`, updateError);
        throw new Error(`Failed to delete connection: ${updateError.message}`);
      }

      // Delete Redis key for the platform
      try {
        const redisKey = `status:${platformPrefix}:${clientId}`;

        const success = await deleteRedisValue(redisKey);

        if (!success) {
          console.warn(`Warning: Failed to delete Redis key for ${platform}`);
          // Continue execution even if Redis deletion fails
        } else {
          console.log(`Successfully deleted Redis key for ${platform}: ${redisKey}`);
        }
      } catch (redisError) {
        console.warn(`Warning: Error deleting Redis key for ${platform}:`, redisError);
        // Continue execution even if Redis deletion fails
      }

      // Wait a moment for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update local state
      if (platform === 'web') {
        // For web platform, clear domain
        if (webDomainRef.current) {
          webDomainRef.current.value = '';
        }
      } else {
        // For other platforms, clear token
        const tokenRef =
          platform === 'facebook' ? facebookTokenRef :
          platform === 'instagram' ? instagramTokenRef :
          /* platform === 'whatsapp' ? whatsappTokenRef : */
          telegramTokenRef;

        if (tokenRef.current) {
          tokenRef.current.value = '';
        }

        // Update token values
        setTokenValues(prev => ({
          ...prev,
          [platform]: ''
        }));
      }

      // Update connected platforms state
      setConnectedPlatforms(prev => ({
        ...prev,
        [platform]: false
      }));

      // Reset edit state
      setEditStates(prev => ({
        ...prev,
        [platform]: { isEditing: false, hasChanged: false }
      }));

      // Show success message
      setSuccessMessage(`Successfully disconnected ${platform}!`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 2000);

    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      setErrorMessage(`Failed to disconnect ${platform}: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      // Reset states
      setIsDeleting(false);
      setShowDeleteConfirmation({ show: false, platform: null });
    }
  };

  // We've removed the initiateConnect and handleConnect functions as they're no longer needed
  // Keeping this as a no-op for now to avoid breaking existing references
  const initiateConnect = (platform: string) => {
    // Get token or domain from input ref
    let value = '';

    switch (platform) {
      case 'facebook':
        value = facebookTokenRef.current?.value || '';
        break;
      case 'instagram':
        value = instagramTokenRef.current?.value || '';
        break;
      /* case 'whatsapp':
        value = whatsappTokenRef.current?.value || '';
        break; */
      case 'telegram':
        value = telegramTokenRef.current?.value || '';
        break;
      case 'web':
        value = webDomainRef.current?.value || '';
        break;
    }

    if (!value || value.trim() === '') {
      const fieldType = platform === 'web' ? 'domain name' : 'access token';
      setErrorMessage(`Please enter a valid ${fieldType} for ${platform}.`);

      // Auto-dismiss error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 1000);

      return;
    }

    // Show confirmation dialog
    setShowConfirmation({
      show: true,
      platform
    });
  };

  // Handle platform connection after confirmation
  const handleConnect = async (platform: string) => {
    try {
      // Hide confirmation dialog
      setShowConfirmation({
        show: false,
        platform: null
      });

      // Clear any previous messages
      setErrorMessage(null);
      setSuccessMessage(null);

      // Get client ID
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        setErrorMessage('Client ID not found. Please ensure you are properly logged in.');
        return;
      }

      // Get token or domain from input ref
      let token = '';
      let domain = '';
      let webhookUrl = '';
      let isWebPlatform = platform === 'web';

      switch (platform) {
        case 'facebook':
          token = facebookTokenRef.current?.value || '';
          webhookUrl = credentials?.fb_url || 'https://api.chhlatbot.com/webhook/messenger';
          break;
        case 'instagram':
          token = instagramTokenRef.current?.value || '';
          webhookUrl = credentials?.ig_url || 'https://api.chhlatbot.com/webhook/instagram';
          break;
        /* case 'whatsapp':
          token = whatsappTokenRef.current?.value || '';
          webhookUrl = credentials?.wa_url || 'https://api.chhlatbot.com/webhook/whatsapp';
          break; */
        case 'telegram':
          token = telegramTokenRef.current?.value || '';
          webhookUrl = ''; // Telegram doesn't have a webhook URL in the credentials table
          break;
        case 'web':
          domain = webDomainRef.current?.value || '';
          webhookUrl = credentials?.web_url || 'https://api.chhlatbot.com/webhook/web';
          break;
        default:
          webhookUrl = '';
      }

      // Check if token or domain is empty
      if (isWebPlatform) {
        if (!domain || domain.trim() === '') {
          setErrorMessage(`Please enter a valid domain name for ${platform}.`);

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 2000);

          return;
        }
      } else {
        if (!token || token.trim() === '') {
          setErrorMessage(`Please enter a valid access token for ${platform}.`);

          // Auto-dismiss error message after 3 seconds
          setTimeout(() => {
            setErrorMessage(null);
          }, 3000);

          return;
        }
      }

      // Set connecting platform
      setConnectingPlatform(platform);

      // Send to API endpoint
      const response = await fetch('/api/webhooks/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: clientId,
          webhook_url: webhookUrl,
          token: isWebPlatform ? domain : token,
          type: platform
        }),
      });

      // Wait for a minimum of 1 second for UX purposes
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Parse the response
      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || `Failed to connect ${platform}`);
      }

      // Make sure it's a success
      if (!responseData.success) {
        throw new Error(`Unexpected response from server when connecting ${platform}`);
      }

      // Save token or domain to database
      if (isWebPlatform) {
        // For web platform, update web_domain
        const { error: updateError } = await supabase
          .from('client_credentials')
          .update({ web_domain: domain })
          .eq('client_id', clientId);

        if (updateError) {
          console.error(`Error saving ${platform} domain:`, updateError);
          throw new Error(`Failed to save domain: ${updateError.message}`);
        }
      } else {
        // For other platforms, update token
        const tokenField = platform === 'facebook' ? 'fb_token' :
                           platform === 'instagram' ? 'ig_token' :
                           /* platform === 'whatsapp' ? 'wa_token' : */
                           'tg_token';

        const { error: updateError } = await supabase
          .from('client_credentials')
          .update({ [tokenField]: token })
          .eq('client_id', clientId);

        if (updateError) {
          console.error(`Error saving ${platform} token:`, updateError);
          throw new Error(`Failed to save token: ${updateError.message}`);
        }
      }

      // Update connected platforms state
      setConnectedPlatforms(prev => ({
        ...prev,
        [platform]: true
      }));

      // Update token values
      setTokenValues(prev => ({
        ...prev,
        [platform]: token
      }));

      // Reset edit state
      setEditStates(prev => ({
        ...prev,
        [platform]: { isEditing: false, hasChanged: false }
      }));

      // Show success message
      setSuccessMessage(`Successfully updated ${platform}!`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 1500);

    } catch (error) {
      console.error(`Error connecting ${platform}:`, error);
      setErrorMessage(`Failed to update ${platform}, please provide a valid token`);

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage(null);
      }, 3000);
    } finally {
      // Reset connecting platform
      setConnectingPlatform(null);
    }
  };

  // Don't render anything server-side
  if (!isClient) return null

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      <div className="flex-grow container mx-auto px-4 pt-4 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Top navigation-like row with logo */}
          <div className="flex justify-center py-2 mb-8">
            <Link href="/dashboard">
              <img
                src="/images/white_tran_logo.svg"
                alt="Chhlat Logo"
                className="h-10 w-auto cursor-pointer hover:opacity-80 transition-opacity"
              />
            </Link>
          </div>

          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <Link href="/dashboard/connect" className="text-sm text-zinc-400 hover:text-white">
                ← {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('edit_connections')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Loading Indicator */}
          {isLoading && (
            <div className="mb-6 p-4 bg-jade-purple/10 border border-jade-purple/30 rounded-lg text-white flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-sm font-body">{t('loading_connection_details')}</p>
            </div>
          )}

          {/* Error Message - Positioned in the middle of the window */}
          {errorMessage && (
            <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50">
              <div className="bg-black/80 border border-red-500/50 rounded-xl p-6 max-w-md mx-auto shadow-lg transform transition-all"
                   style={{
                     boxShadow: '0 0 20px rgba(220, 38, 38, 0.3), inset 0 0 20px rgba(220, 38, 38, 0.2)'
                   }}>
                <div className="flex items-center justify-center mb-4">
                  <div className="bg-red-500/20 rounded-full p-3">
                    <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-white text-center mb-2 font-title">{t('error')}</h3>
                <p className="text-center text-red-200 font-body">{errorMessage}</p>
              </div>
            </div>
          )}

          {/* Success Message - Positioned in the middle of the window */}
          {successMessage && (
            <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50">
              <div className="bg-black/80 border border-green-500/50 rounded-xl p-6 max-w-md mx-auto shadow-lg transform transition-all"
                   style={{
                     boxShadow: '0 0 20px rgba(74, 222, 128, 0.3), inset 0 0 20px rgba(74, 222, 128, 0.2)'
                   }}>
                <div className="flex items-center justify-center mb-4">
                  <div className="bg-green-500/20 rounded-full p-3">
                    <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-white text-center mb-2 font-title">{t('success')}</h3>
                <p className="text-center text-green-200 font-body">{successMessage}</p>
              </div>
            </div>
          )}

          {/* Update Confirmation Dialog */}
          {showConfirmation.show && (
            <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50">
              <div className="bg-black/80 border border-jade-purple/50 rounded-xl p-6 max-w-md mx-auto shadow-lg transform transition-all"
                   style={{
                     boxShadow: '0 0 20px rgba(134, 107, 255, 0.3), inset 0 0 20px rgba(134, 107, 255, 0.2)'
                   }}>
                <div className="flex items-center justify-center mb-4">
                  <div className="bg-jade-purple/20 rounded-full p-3">
                    <svg className="w-8 h-8 text-jade-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-white text-center mb-2 font-title">{t('confirm_update')}</h3>
                <p className="text-center text-white/80 mb-6 font-body">
                  {showConfirmation.platform === 'web'
                    ? t('confirm_update_web').replace('{platform}', showConfirmation.platform || '')
                    : t('confirm_update_platform').replace('{platform}', showConfirmation.platform || '')
                  }
                </p>
                <div className="flex justify-center gap-4 w-full">
                  <button
                    onClick={() => setShowConfirmation({ show: false, platform: null })}
                    className="flex-1 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body"
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={() => handleConnect(showConfirmation.platform!)}
                    className="flex-1 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body"
                    style={{
                      boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                    }}
                  >
                    {t('update')}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Dialog */}
          {showDeleteConfirmation.show && (
            <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50">
              <div
                ref={deleteConfirmModalRef}
                className="bg-red-500/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-white/25 text-zinc-300 rounded-full p-3">
                    {isDeleting ? (
                      <div className="w-4 h-4 border-2 border-zinc-300 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <FaTrash className="w-4 h-4" />
                    )}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {isDeleting ? t('disconnecting') : t('confirm_disconnect')}
                </h3>
                <p className="text-zinc-300 mb-6 text-center">
                  {isDeleting
                    ? t('please_wait_disconnect').replace('{platform}', showDeleteConfirmation.platform || '')
                    : t('confirm_disconnect_message').replace('{platform}', showDeleteConfirmation.platform || '').replace('{type}', showDeleteConfirmation.platform === 'web' ? t('domain') : t('token'))
                  }
                </p>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteConfirmation({ show: false, platform: null })}
                    className={`flex-1 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors ${isDeleting ? 'opacity-50 cursor-not-allowed' : ''}`}
                    style={{ display: isDeleting ? 'none' : 'block' }}
                    disabled={isDeleting}
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={() => handleDelete(showDeleteConfirmation.platform!)}
                    className={`flex-1 py-2 ${isDeleting ? 'bg-red-700 cursor-wait' : 'bg-red-600 hover:bg-red-700'} text-white rounded-lg transition-colors`}
                    style={{ display: isDeleting ? 'none' : 'block' }}
                    disabled={isDeleting}
                  >
                    {t('disconnect')}
                  </button>
                  {isDeleting && (
                    <div className="flex-1 py-2.5 text-center text-zinc-300 font-body">
                      <span className="text-sm">{t('processing')}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Connected Platforms for Editing */}
          <div className="space-y-6">
            {/* Facebook Messenger */}
            {connectedPlatforms.facebook && (
              <div className="bg-deep-blue/60 backdrop-blur-lg border border-white/30 rounded-xl p-6 hover:border-white/50 transition-all relative" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.1)'
              }}>
              {/* Edit button in top right corner */}
              <button
                className="absolute top-4 right-4 text-zinc-400 hover:text-white bg-black/30 rounded-full p-1.5 transition-colors"
                onClick={() => toggleEditMode('facebook')}
                title={editStates.facebook.isEditing ? "Cancel editing" : "Edit token"}
              >
                {editStates.facebook.isEditing ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                )}
              </button>



              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-blue-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                  <FaFacebookMessenger size={18} />
                </div>
                <div style={{ flex: 1 }}>
                  <h3 className="font-medium text-white text-lg font-title">{t('facebook_messenger')}</h3>
                  <p className="text-zinc-400 text-sm font-body">{t('manage_facebook_connection')}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative flex-1">
                    <input
                      ref={facebookTokenRef}
                      type="password"
                      placeholder="Enter Page Access Token"
                      defaultValue={tokenValues.facebook}
                      className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      disabled={!editStates.facebook.isEditing || connectingPlatform === 'facebook'}
                    />
                  </div>
                  {editStates.facebook.isEditing && (
                    <button
                      className="bg-red-600 hover:bg-red-700 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                      style={{
                        boxShadow: '0 0 10px rgba(220, 38, 38, 0.3)'
                      }}
                      onClick={() => initiateDelete('facebook')}
                      disabled={connectingPlatform === 'facebook'}
                    >
                      {connectingPlatform === 'facebook' ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('processing')}
                        </>
                      ) : (
                        <>
                          {t('disconnect')}
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
            )}

            {/* Instagram DM */}
            {connectedPlatforms.instagram && (
              <div className="bg-deep-blue/60 backdrop-blur-lg border border-white/30 rounded-xl p-6 hover:border-white/50 transition-all relative" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.1)'
              }}>
              {/* Edit button in top right corner */}
              <button
                className="absolute top-4 right-4 text-zinc-400 hover:text-white bg-black/30 rounded-full p-1.5 transition-colors"
                onClick={() => toggleEditMode('instagram')}
                title={editStates.instagram.isEditing ? "Cancel editing" : "Edit token"}
              >
                {editStates.instagram.isEditing ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                )}
              </button>



              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-pink-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                  <FaInstagram size={20} />
                </div>
                <div style={{ flex: 1 }}>
                  <h3 className="font-medium text-white text-lg font-title">{t('instagram_dm')}</h3>
                  <p className="text-zinc-400 text-sm font-body">{t('manage_instagram_connection')}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative flex-1">
                    <input
                      ref={instagramTokenRef}
                      type="password"
                      placeholder="Enter Page Access Token"
                      defaultValue={tokenValues.instagram}
                      className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      disabled={!editStates.instagram.isEditing || connectingPlatform === 'instagram'}
                    />
                  </div>
                  {editStates.instagram.isEditing && (
                    <button
                      className="bg-red-600 hover:bg-red-700 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                      style={{
                        boxShadow: '0 0 10px rgba(220, 38, 38, 0.3)'
                      }}
                      onClick={() => initiateDelete('instagram')}
                      disabled={connectingPlatform === 'instagram'}
                    >
                      {connectingPlatform === 'instagram' ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('processing')}
                        </>
                      ) : (
                        <>
                          {t('disconnect')}
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
            )}

            {/* WhatsApp */}
            {/* {connectedPlatforms.whatsapp && (
              <div className="bg-deep-blue/60 backdrop-blur-lg border border-white/30 rounded-xl p-6 hover:border-white/50 transition-all relative" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.1)'
              }}>

              <button
                className="absolute top-4 right-4 text-zinc-400 hover:text-white bg-black/30 rounded-full p-1.5 transition-colors"
                onClick={() => toggleEditMode('whatsapp')}
                title={editStates.whatsapp.isEditing ? "Cancel editing" : "Edit token"}
              >
                {editStates.whatsapp.isEditing ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                )}
              </button>



              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-white/10  rounded-full flex items-center justify-center mr-4 text-green-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                  <FaWhatsapp size={22} />
                </div>
                <div style={{ flex: 1 }}>
                  <h3 className="font-medium text-white text-lg font-title">WhatsApp</h3>
                  <p className="text-zinc-400 text-sm font-body">Copy webhook URL and provide your page access token</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="relative">
                  <div className="flex items-center">
                    <div
                      className="flex-1 bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-2.5 text-sm text-zinc-400 font-body"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                    >
                      ••••••••••••••••••
                    </div>
                    <div className="absolute right-4 flex gap-2">
                      <button
                        className="text-zinc-400 hover:text-white"
                        onClick={() => handleCopyWebhook('wa-webhook')}
                        title="Copy webhook URL"
                      >
                        {webhookStates['wa-webhook'].isCopied ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative flex-1">
                    <input
                      ref={whatsappTokenRef}
                      type="password"
                      placeholder="Enter Page Access Token"
                      defaultValue={tokenValues.whatsapp}
                      className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      disabled={!editStates.whatsapp.isEditing || connectingPlatform === 'whatsapp'}
                    />
                  </div>
                  {editStates.whatsapp.isEditing && (
                    <button
                      className="bg-red-600 hover:bg-red-700 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                      style={{
                        boxShadow: '0 0 10px rgba(220, 38, 38, 0.3)'
                      }}
                      onClick={() => initiateDelete('whatsapp')}
                      disabled={connectingPlatform === 'whatsapp'}
                    >
                      {connectingPlatform === 'whatsapp' ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Processing...
                        </>
                      ) : (
                        <>
                          Disconnect
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
            )} */}

            {/* Telegram */}
            {connectedPlatforms.telegram && (
              <div className="bg-deep-blue/60 backdrop-blur-lg border border-white/30 rounded-xl p-6 hover:border-white/50 transition-all relative" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.1)'
              }}>
              {/* Edit button in top right corner */}
              <button
                className="absolute top-4 right-4 text-zinc-400 hover:text-white bg-black/30 rounded-full p-1.5 transition-colors"
                onClick={() => toggleEditMode('telegram')}
                title={editStates.telegram.isEditing ? "Cancel editing" : "Edit token"}
              >
                {editStates.telegram.isEditing ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                )}
              </button>



              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center mr-4 text-blue-400 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                  <FaPaperPlane size={16} />
                </div>
                <div style={{ flex: 1 }}>
                  <h3 className="font-medium text-white text-lg font-title">{t('telegram')}</h3>
                  <p className="text-zinc-400 text-sm font-body">{t('manage_telegram_connection')}</p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <input
                    ref={telegramTokenRef}
                    type="password"
                    placeholder="Enter Telegram Bot API Token"
                    defaultValue={tokenValues.telegram}
                    className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                    disabled={!editStates.telegram.isEditing || connectingPlatform === 'telegram'}
                  />
                </div>
                {editStates.telegram.isEditing && (
                  <button
                    className="bg-red-600 hover:bg-red-700 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                    style={{
                      boxShadow: '0 0 10px rgba(220, 38, 38, 0.3)'
                    }}
                    onClick={() => initiateDelete('telegram')}
                    disabled={connectingPlatform === 'telegram'}
                  >
                    {connectingPlatform === 'telegram' ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('processing')}
                      </>
                    ) : (
                      <>
                        {t('disconnect')}
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
            )}

            {/* Web API */}
            {connectedPlatforms.web && (
              <div className="bg-deep-blue/60 backdrop-blur-lg border border-white/30 rounded-xl p-6 hover:border-white/50 transition-all relative" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.1)'
              }}>
              {/* Edit button in top right corner */}
              <button
                className="absolute top-4 right-4 text-zinc-400 hover:text-white bg-black/30 rounded-full p-1.5 transition-colors"
                onClick={() => toggleEditMode('web')}
                title={editStates.web.isEditing ? "Cancel editing" : "Edit domain"}
              >
                {editStates.web.isEditing ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                )}
              </button>



              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-white/10  rounded-full flex items-center justify-center mr-4 text-purple-500 shadow-lg border border-white/20" style={{ minWidth: '2.5rem' }}>
                  <FaGlobe size={20} />
                </div>
                <div style={{ flex: 1 }}>
                  <h3 className="font-medium text-white text-lg font-title">{t('web_api')}</h3>
                  <p className="text-zinc-400 text-sm font-body">{t('manage_web_connection')}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative flex-1">
                    <input
                      ref={webDomainRef}
                      type="text"
                      placeholder="Enter Website Domain (e.g., example.com)"
                      defaultValue={credentials?.web_domain || ''}
                      className="w-full bg-black/30 border border-white/20 rounded-lg px-4 py-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-jade-purple/50 focus:border-jade-purple/50 font-body"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      disabled={!editStates.web.isEditing || connectingPlatform === 'web'}
                    />
                  </div>
                  {editStates.web.isEditing && (
                    <button
                      className="bg-red-600 hover:bg-red-700 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
                      style={{
                        boxShadow: '0 0 10px rgba(220, 38, 38, 0.3)'
                      }}
                      onClick={() => initiateDelete('web')}
                      disabled={connectingPlatform === 'web'}
                    >
                      {connectingPlatform === 'web' ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('processing')}
                        </>
                      ) : (
                        <>
                          {t('disconnect')}
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
            )}

            {/* No connected platforms message */}
            {Object.values(connectedPlatforms).filter(Boolean).length === 0 && (
              <div className="bg-deep-blue/40 backdrop-blur-lg border border-white/20 rounded-lg p-6 text-center">
                <p className="text-zinc-400 font-body">{t('no_platforms_connected_edit')}</p>
                <Link href="/dashboard/connect" className="mt-4 inline-block bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body">
                  {t('connect_platforms')}
                </Link>
              </div>
            )}
          </div>

          {/* <div className="mt-8 flex justify-center">
            <Link href="/dashboard/connect" className="bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors font-body">
              Back to Connections
            </Link>
          </div> */}
        </motion.div>
      </div>

      <Footer />
    </div>
  )
}