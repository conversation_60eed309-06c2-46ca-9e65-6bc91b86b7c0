'use client'

import { useState, useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'
import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { getClientInfo } from '@/utils/client'
import { FaBrain, FaImage, FaComments, FaSignOutAlt, FaExclamationTriangle, FaTimes } from 'react-icons/fa'
import { v4 as uuidv4 } from 'uuid'
import { sendWelcomeUpdateWebhook } from '@/app/api/webhooks/welcome'
import { useLanguage } from '@/context/LanguageContext'

// PhotoSearchDropdown component using createPortal
const PhotoSearchDropdown = ({
  show,
  results,
  position,
  onSelectPhoto
}: {
  show: boolean,
  results: Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>,
  position: {top: number, left: number, width: number} | null,
  onSelectPhoto: (photo: any) => void
}) => {
  if (!show || !position || typeof window === 'undefined') return null;

  return createPortal(
    <div
      className="photo-search-dropdown fixed z-[9999] bg-jade-purple/90 border border-white/20 rounded-lg shadow-lg max-h-60 overflow-y-auto backdrop-blur-sm"
      style={{
        boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), 0 0 20px rgba(134, 107, 255, 0.3), inset 0 0 20px rgba(255, 255, 255, 0.1)',
        top: `${position.top}px`,
        left: `${position.left}px`,
        width: `${position.width}px`,
        transform: 'translateY(5px)', // Add a small offset for better appearance
        position: 'fixed', // Ensure fixed positioning
        willChange: 'transform', // Optimize for animations/transitions
        overscrollBehavior: 'contain' // Prevent parent scrolling when dropdown is at boundary
      }}
    >
      {results.length > 0 ? (
        results.map(photo => (
          <div
            key={photo.id}
            className="flex items-center gap-3 p-3 hover:bg-jade-purple-dark cursor-pointer border-b border-white/10 last:border-0"
            style={{
              transition: 'all 0.2s ease'
            }}
            onClick={() => onSelectPhoto(photo)}
          >
            {/* Photo Thumbnail */}
            <div className="w-10 h-10 bg-black/30 border border-white/20 rounded overflow-hidden flex-shrink-0" style={{
              boxShadow: 'inset 0 0 5px rgba(255, 255, 255, 0.1)'
            }}>
              {photo.photo_url && photo.photo_url.length > 0 ? (
                <img
                  src={photo.photo_url[0]}
                  alt={photo.photo_id}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-zinc-600 text-zinc-400">
                  <span>No Image</span>
                </div>
              )}
            </div>
            {/* Photo ID */}
            <div className="flex-1 truncate">
              <p className="text-white truncate">{photo.photo_id}</p>
            </div>
          </div>
        ))
      ) : (
        <div className="p-3 text-white/50 text-center">
          No photos found
        </div>
      )}
    </div>,
    document.body
  );
};

export default function IntrosOutrosPage() {
  // Stats state
  const [totalFaqs, setTotalFaqs] = useState(0)
  const [photoCount, setPhotoCount] = useState(0)
  const [isLoadingCount, setIsLoadingCount] = useState(true)

  // Subscription limits
  const [totalFaqsLimit, setTotalFaqsLimit] = useState(0)
  const [photoLimit, setPhotoLimit] = useState(0)
  const [faqUsagePercentage, setFaqUsagePercentage] = useState(0)
  const [photoUsagePercentage, setPhotoUsagePercentage] = useState(0)

  // Create Supabase client
  const supabase = createClientComponentClient()
  const { t } = useLanguage()

  // Photo search state - separate for intro and outro
  const [introSearchQuery, setIntroSearchQuery] = useState('')
  const [outroSearchQuery, setOutroSearchQuery] = useState('')
  const [introSearchResults, setIntroSearchResults] = useState<any[]>([])
  const [outroSearchResults, setOutroSearchResults] = useState<any[]>([])
  const [showIntroResults, setShowIntroResults] = useState(false)
  const [showOutroResults, setShowOutroResults] = useState(false)
  const [isSearchingIntro, setIsSearchingIntro] = useState(false)
  const [isSearchingOutro, setIsSearchingOutro] = useState(false)
  const [selectedIntroPhoto, setSelectedIntroPhoto] = useState<any>(null)
  const [selectedOutroPhoto, setSelectedOutroPhoto] = useState<any>(null)

  // Loading animation states for photo selection
  const [isIntroPhotoLoading, setIsIntroPhotoLoading] = useState(false)
  const [isOutroPhotoLoading, setIsOutroPhotoLoading] = useState(false)
  const outroSearchResultsRef = useRef<HTMLDivElement>(null)
  const introSearchInputRef = useRef<HTMLInputElement>(null)
  const [introDropdownPosition, setIntroDropdownPosition] = useState<{top: number, left: number, width: number} | null>(null)

  // Add state for all photos
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Image gallery state
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const [isZoomed, setIsZoomed] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Audio recording state
  const [isRecording, setIsRecording] = useState(false)
  const [recordingFor, _setRecordingFor] = useState<'intro' | 'outro' | null>(null)

  // Custom setter for recordingFor to track changes
  const setRecordingFor = (value: 'intro' | 'outro' | null) => {
    // Don't clear any audio UI when starting recording
    _setRecordingFor(value);
  }
  const [recordingTime, setRecordingTime] = useState(0)
  const [introAudioUrl, setIntroAudioUrl] = useState<string | null>(null)
  const [outroAudioUrl, setOutroAudioUrl] = useState<string | null>(null)
  const [recordingError, setRecordingError] = useState<string | null>(null)
  const [showRecordingPopup, setShowRecordingPopup] = useState(false)

  // Add new state for showing audio error popup
  const [showAudioErrorPopup, setShowAudioErrorPopup] = useState(false)
  // Add state to track which section has the error
  const [audioErrorSection, setAudioErrorSection] = useState<'intro' | 'outro' | null>(null)
  // Add state to track if a recording is over 60 seconds
  const [isRecordingTooLong, setIsRecordingTooLong] = useState(false)

  // CRITICAL FIX: Add state to track if a recording was too long for each section
  const [introRecordingTooLong, setIntroRecordingTooLong] = useState(false)
  const [outroRecordingTooLong, setOutroRecordingTooLong] = useState(false)

  // Separate recording state indicators for intro and outro
  // Commented out since we now have the animation recording in the window popup
  // const [isIntroRecording, setIsIntroRecording] = useState(false)
  // const [isOutroRecording, setIsOutroRecording] = useState(false)

  // Enhanced recording state
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [audioRecorder, setAudioRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null)
  const [audioInitialized, setAudioInitialized] = useState(false)

  // Refs for timers and audio
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Audio playback state
  const [isPlaying, setIsPlaying] = useState<'intro' | 'outro' | null>(null)
  const [audioPlayer, setAudioPlayer] = useState<HTMLAudioElement | null>(null)
  const [playbackProgress, setPlaybackProgress] = useState(0)
  const [playbackTime, setPlaybackTime] = useState(0)
  const [audioDuration, setAudioDuration] = useState(0)
  const [introAudioDuration, setIntroAudioDuration] = useState(0)
  const [outroAudioDuration, setOutroAudioDuration] = useState(0)

  // Text content state
  const [introText, _setIntroText] = useState('')
  const [outroText, _setOutroText] = useState('')

  // Custom setters to track changes
  const setIntroText = (value: string) => {
    _setIntroText(value);
  }

  const setOutroText = (value: string) => {
    _setOutroText(value);
  }
  const [isIntroSaving, setIsIntroSaving] = useState(false)
  const [isOutroSaving, setIsOutroSaving] = useState(false)

  // Audio field saving indicators (separate from the save button)
  const [isIntroAudioSaving, setIsIntroAudioSaving] = useState(false)
  const [isOutroAudioSaving, setIsOutroAudioSaving] = useState(false)

  const [isUpdating, setIsUpdating] = useState(false)

  // Edit mode state
  const [isIntroEditing, setIsIntroEditing] = useState(false)
  const [isOutroEditing, setIsOutroEditing] = useState(false)
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  const [confirmationSection, setConfirmationSection] = useState<'intro' | 'outro' | null>(null)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [updateMessage, setUpdateMessage] = useState('')

  // Initial state tracking for detecting changes
  const [initialIntroText, setInitialIntroText] = useState('')
  const [initialOutroText, setInitialOutroText] = useState('')
  const [initialIntroAudioUrl, setInitialIntroAudioUrl] = useState<string | null>(null)
  const [initialOutroAudioUrl, setInitialOutroAudioUrl] = useState<string | null>(null)
  const [initialIntroAudioDuration, setInitialIntroAudioDuration] = useState(0)
  const [initialOutroAudioDuration, setInitialOutroAudioDuration] = useState(0)
  const [initialSelectedIntroPhoto, setInitialSelectedIntroPhoto] = useState<any>(null)
  const [initialSelectedOutroPhoto, setInitialSelectedOutroPhoto] = useState<any>(null)
  const [hasIntroChanges, setHasIntroChanges] = useState(false)
  const [hasOutroChanges, setHasOutroChanges] = useState(false)

  // Audio deletion confirmation
  const [showDeleteAudioConfirmation, setShowDeleteAudioConfirmation] = useState(false)
  const [audioToDelete, setAudioToDelete] = useState<'intro' | 'outro' | null>(null)

  // Editing state
  const [editingItem, setEditingItem] = useState<{
    section: 'intro' | 'outro' | null;
    value: string;
  } | null>(null)

  // Ref for textarea auto-focus
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Fetch FAQ count
  const fetchFaqCount = async () => {
    try {
      const clientId = getClientInfo()?.client_id;

      if (!clientId) {
        console.error('Client ID not found while fetching FAQ count');
        setTotalFaqs(0);
        return;
      }

      const { count, error } = await supabase
        .from('faqs')
        .select('client_id', { count: 'exact', head: true })
        .eq('client_id', clientId);

      if (error) {
        console.error('Error fetching FAQ count:', error);
        return;
      }

      setTotalFaqs(count || 0);

      // Fetch subscription info to calculate usage limits
      await fetchSubscriptionLimits();
    } catch (error) {
      console.error('Error in fetchFaqCount:', error);
    } finally {
      setIsLoadingCount(false);
    }
  }

  // Fetch photo count
  const fetchPhotoCount = async () => {
    try {
      const clientId = getClientInfo()?.client_id;

      if (!clientId) {
        console.error('Client ID not found while fetching photo count');
        setPhotoCount(0);
        return;
      }

      const { count, error } = await supabase
        .from('photos')
        .select('client_id', { count: 'exact', head: true })
        .eq('client_id', clientId);

      if (error) {
        console.error('Error fetching photo count:', error);
        return;
      }

      setPhotoCount(count || 0);
    } catch (error) {
      console.error('Error in fetchPhotoCount:', error);
    }
  }

  // Fetch subscription plan limits
  const fetchSubscriptionLimits = async () => {
    try {
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;
      const planType = clientInfo?.subscription_tier;

      if (!clientId) {
        console.error('Client ID not found while fetching subscription limits');
        return;
      }

      if (!planType) {
        console.error('No subscription plan found in client info');
        return;
      }

      // Get all plans and filter in JavaScript instead of using eq()
      const { data: allPlans, error: planError } = await supabase
        .from('plans')
        .select('name, total_faqs, total_photos');

      if (planError) {
        console.error('Error fetching plans data:', planError);
        return;
      }

      // Find the matching plan with case-insensitive comparison
      const planData = allPlans?.find(plan =>
        plan.name.trim().toLowerCase() === planType.trim().toLowerCase()
      );

      if (!planData) {
        console.error('No matching plan found for:', planType);
        // Set default values to 0 to make it clear there's an issue
        setTotalFaqsLimit(0);
        setPhotoLimit(0);
        return;
      }

      // Set the limits
      setTotalFaqsLimit(planData.total_faqs || 0);
      setPhotoLimit(planData.total_photos || 0);

      // Calculate usage percentages
      if (planData.total_faqs > 0) {
        const faqPercentage = (totalFaqs / planData.total_faqs) * 100;
        setFaqUsagePercentage(Math.min(faqPercentage, 100));
      }

      if (planData.total_photos > 0) {
        const photoPercentage = (photoCount / planData.total_photos) * 100;
        setPhotoUsagePercentage(Math.min(photoPercentage, 100));
      }
    } catch (error) {
      console.error('Error in fetchSubscriptionLimits:', error);
    }
  }

  // Format recording time
  const formatRecordingTime = (seconds: number) => {
    // During recording, just return the seconds
    return `${seconds}s`;
  };

  // Handle intro photo search
  const handleIntroPhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setIntroSearchQuery(query);
    searchIntroPhotos(query);

    // Update dropdown position immediately when typing
    if (introSearchInputRef.current && query.trim() !== '') {
      const rect = introSearchInputRef.current.getBoundingClientRect();
      setIntroDropdownPosition({
        top: rect.bottom,
        left: rect.left,
        width: rect.width
      });
    }
  };

  // Handle outro photo search
  const handleOutroPhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setOutroSearchQuery(query);
    searchOutroPhotos(query);
  };

  // Fetch all photos from Supabase
  const fetchAllPhotos = async () => {
    try {
      const clientId = getClientInfo()?.client_id;
      if (!clientId) {
        console.error('Client ID not found while fetching photos');
        return;
      }

      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching photos:', error);
        return;
      }

      setAllPhotos(data || []);
    } catch (error) {
      console.error('Error in fetchAllPhotos:', error);
    }
  };

  // Search photos for intro section
  const searchIntroPhotos = async (query: string) => {
    if (!query.trim()) {
      setIntroSearchResults([]);
      setShowIntroResults(false);
      return;
    }

    setIsSearchingIntro(true);
    try {
      // First try to search locally if we have photos cached
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);

        if (filteredPhotos.length > 0) {
          setIntroSearchResults(filteredPhotos);
          setShowIntroResults(true);
          setIsSearchingIntro(false);
          return;
        }
      }

      // If no local results or no local data, fetch from server
      const clientId = getClientInfo()?.client_id;

      if (!clientId) {
        console.error('Client ID not found while searching photos');
        return;
      }

      // Search photos that match the query by photo_id
      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .ilike('photo_id', `%${query}%`)
        .limit(5);

      if (error) {
        console.error('Error searching photos:', error);
        return;
      }

      setIntroSearchResults(data || []);
      setShowIntroResults(data && data.length > 0);
    } catch (error) {
      console.error('Error in searchIntroPhotos:', error);
    } finally {
      setIsSearchingIntro(false);
    }
  };

  // Search photos for outro section
  const searchOutroPhotos = async (query: string) => {
    if (!query.trim()) {
      setOutroSearchResults([]);
      setShowOutroResults(false);
      return;
    }

    setIsSearchingOutro(true);
    try {
      // First try to search locally if we have photos cached
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);

        if (filteredPhotos.length > 0) {
          setOutroSearchResults(filteredPhotos);
          setShowOutroResults(true);
          setIsSearchingOutro(false);
          return;
        }
      }

      // If no local results or no local data, fetch from server
      const clientId = getClientInfo()?.client_id;

      if (!clientId) {
        console.error('Client ID not found while searching photos');
        return;
      }

      // Search photos that match the query by photo_id
      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .ilike('photo_id', `%${query}%`)
        .limit(5);

      if (error) {
        console.error('Error searching photos:', error);
        return;
      }

      setOutroSearchResults(data || []);
      setShowOutroResults(data && data.length > 0);
    } catch (error) {
      console.error('Error in searchOutroPhotos:', error);
    } finally {
      setIsSearchingOutro(false);
    }
  };

  // Handle selecting a photo from intro search results
  const handleSelectIntroPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null;

    const processedPhoto = {
      id: photo.id,
      photo_id: photo.photo_id,
      photo_url: thumbnail,
      full_photo_urls: photo.photo_url // Store the complete array of photo URLs
    };

    // Show loading animation
    setIsIntroPhotoLoading(true);

    // Hide dropdown and clear search query immediately
    setShowIntroResults(false);
    setIntroSearchQuery(''); // Clear the search query after selection

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedIntroPhoto(processedPhoto);

      // Update change tracking
      const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
      setHasIntroChanges(
        initialPhotoId !== photo.photo_id ||
        introText !== initialIntroText ||
        (introAudioUrl !== initialIntroAudioUrl)
      );

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsIntroPhotoLoading(false);
      }, 50);
    }, 100);
  };

  // Handle selecting a photo from outro search results
  const handleSelectOutroPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null;

    const processedPhoto = {
      id: photo.id,
      photo_id: photo.photo_id,
      photo_url: thumbnail,
      full_photo_urls: photo.photo_url // Store the complete array of photo URLs
    };

    // Show loading animation
    setIsOutroPhotoLoading(true);

    // Hide dropdown and clear search query immediately
    setShowOutroResults(false);
    setOutroSearchQuery(''); // Clear the search query after selection

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedOutroPhoto(processedPhoto);

      // Update change tracking
      const initialPhotoId = initialSelectedOutroPhoto?.photo_id;
      setHasOutroChanges(
        initialPhotoId !== photo.photo_id ||
        outroText !== initialOutroText ||
        (outroAudioUrl !== initialOutroAudioUrl)
      );

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsOutroPhotoLoading(false);
      }, 50);
    }, 100);
  };

  // Clear selected photo
  const handleClearSelectedPhoto = (e: React.MouseEvent, section: 'intro' | 'outro') => {
    e.stopPropagation();
    if (section === 'intro') {
      setSelectedIntroPhoto(null);
      setIntroSearchQuery('');

      // Update change tracking
      const hadInitialPhoto = initialSelectedIntroPhoto !== null;
      setHasIntroChanges(
        hadInitialPhoto ||
        introText !== initialIntroText ||
        (introAudioUrl !== initialIntroAudioUrl)
      );
    } else {
      setSelectedOutroPhoto(null);
      setOutroSearchQuery('');

      // Update change tracking
      const hadInitialPhoto = initialSelectedOutroPhoto !== null;
      setHasOutroChanges(
        hadInitialPhoto ||
        outroText !== initialOutroText ||
        (outroAudioUrl !== initialOutroAudioUrl)
      );
    }
  };

  // View image in gallery
  const handleViewImage = (urls: string[] | null) => {
    if (!urls || urls.length === 0) {
      console.error('No images available to view');
      return;
    }

    // Filter out any invalid URLs
    const validUrls = urls.filter(url => url && url.trim() !== '');

    if (validUrls.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    // Open the gallery modal
    setImageGallery({
      urls: validUrls,
      currentIndex: 0
    });
    setIsZoomed(false); // Reset zoom state
  };

  // Navigate to previous image
  const showPreviousImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
    setIsZoomed(false); // Reset zoom state
  };

  // Navigate to next image
  const showNextImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
    setIsZoomed(false); // Reset zoom state
  };

  // Handle image click for zoom toggle
  const handleImageClick = () => {
    setIsZoomed(!isZoomed);
  };

  // Touch event handlers for swipe navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
    setTouchEnd(null);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    // If zoomed, don't allow swiping
    if (isZoomed) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      showNextImage();
    }

    if (isRightSwipe) {
      showPreviousImage();
    }
  };

  // Initialize audio context for better browser compatibility
  const initializeAudio = () => {
    // Check if already initialized or if running on server
    if (typeof window === 'undefined') return;

    try {
      // Create AudioContext after user interaction
      const context = new (window.AudioContext || (window as any).webkitAudioContext)();
      setAudioContext(context);
      setAudioInitialized(true);

      // Optional: Resume context if it's suspended (iOS sometimes starts it suspended)
      if (context.state === 'suspended') {
        context.resume().catch(e => console.error('Error resuming AudioContext:', e));
      }
    } catch (e) {
      console.error('Error initializing AudioContext:', e);
      setRecordingError('Failed to initialize audio. Please ensure your browser supports Web Audio API and permissions are granted.');
    }
  };

  // Start recording audio with enhanced quality
  const startRecording = async (section: 'intro' | 'outro') => {

    try {
      // Store current audio URLs to preserve them
      const currentIntroAudioUrl = introAudioUrl;
      const currentOutroAudioUrl = outroAudioUrl;

      // If already recording, stop it first
      if (isRecording) {
        stopRecording();
        // Return early to prevent starting a new recording immediately
        // This allows the UI to reset properly
        return;
      }

      // Make sure we don't lose the audio URLs when starting recording
      if (introAudioUrl !== currentIntroAudioUrl && currentIntroAudioUrl) {
        setIntroAudioUrl(currentIntroAudioUrl);
      }

      if (outroAudioUrl !== currentOutroAudioUrl && currentOutroAudioUrl) {
        setOutroAudioUrl(currentOutroAudioUrl);
      }

      // Explicitly reset recording time to 0 and ensure it's updated immediately
      setRecordingTime(0);

      // Clear any previous error states
      setRecordingError(null);
      setAudioErrorSection(null);

      // Reset the recording too long flag
      setIsRecordingTooLong(false);

      // Enhanced audio constraints for better quality
      const audioConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          sampleSize: 16,
          channelCount: 1  // Mono for speech clarity
        }
      };

      // Get audio stream with enhanced constraints
      const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
      setAudioStream(stream);
      // Store the section being recorded without affecting UI state
      setRecordingFor(section);

      // Prefer high-quality codec options
      const supportedTypes = [
        'audio/mp4;codecs=mp4a.40.2', // AAC encoding
        'audio/mp4',
        'audio/webm;codecs=opus', // Best quality for voice
        'audio/webm',
        'audio/mp3',
        'audio/ogg'
      ];

      let selectedMimeType = '';
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          selectedMimeType = type;
          break;
        }
      }

      if (!selectedMimeType) {
        console.error("[startRecording] No supported mimeType found for MediaRecorder!");
        alert("Your browser doesn't support audio recording in a compatible format.");
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
        return;
      }

      const chunks: Blob[] = [];
      setAudioChunks([]);

      // Set higher bitrate for better quality
      const recorderOptions = {
        mimeType: selectedMimeType,
        audioBitsPerSecond: 128000  // 128kbps for good voice quality
      };

      const recorder = new MediaRecorder(stream, recorderOptions);
      setAudioRecorder(recorder);

      recorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data);
        }
      };

      recorder.onstop = () => {
        // Store the current recording section in a local variable to ensure it doesn't change
        // This is crucial because by the time the setTimeout callback runs, recordingFor might be null
        const sectionToUpdate = section;

        // CRITICAL FIX: Use the global variables set in stopRecording to get the actual recording duration
        // This ensures we have the correct duration even if the state has been reset
        const actualRecordingDuration = (window as any)._lastRecordingDuration || 0;
        const actualRecordingSection = (window as any)._lastRecordingSection || sectionToUpdate;

        // CRITICAL FIX: Force a hard-coded check using the actual recording duration from the global variable
        // This ensures we catch recordings over 60 seconds regardless of any state issues
        if (actualRecordingDuration > 60) {

          // Set the recording too long flag to true
          // This will be checked in the setTimeout callback to prevent creating the audio URL
          setIsRecordingTooLong(true);

          // CRITICAL FIX: Also set the section-specific recording too long flag
          // Use both the section from the function parameter and the global variable
          // to ensure we catch all cases
          const actualSection = sectionToUpdate || (window as any)._lastRecordingSection || 'intro';

          if (actualSection === 'intro') {
            setIntroRecordingTooLong(true);
            // Store in a global variable as well to ensure it persists
            (window as any)._introRecordingTooLong = true;
          } else if (actualSection === 'outro') {
            setOutroRecordingTooLong(true);
            // Store in a global variable as well to ensure it persists
            (window as any)._outroRecordingTooLong = true;
          }

          // Set error state and section
          setRecordingError("Over 60s");
          setAudioErrorSection(sectionToUpdate);

          // Show the error popup
          setShowAudioErrorPopup(true);

          // Stop all audio tracks
          if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          }

          // End any saving animations that might have started
          if (sectionToUpdate === 'intro') {
            setIsIntroAudioSaving(false);
          } else if (sectionToUpdate === 'outro') {
            setIsOutroAudioSaving(false);
          }

          // Reset recording state
          setIsRecording(false);
          setRecordingFor(null);
          setShowRecordingPopup(false);

          // CRITICAL FIX: Don't create any audio blob or URL for recordings over 60 seconds
          // This ensures that no audio URL is created or assigned
          chunks.length = 0; // Clear the chunks array to prevent creating a blob

          return; // Exit early without creating the audio URL
        }

        // If we get here, the recording is valid (under 60 seconds)
        // Reset the recording too long flag
        setIsRecordingTooLong(false);

        // If we get here, the recording is valid (under 60 seconds)
        // Use the same selectedMimeType when creating the Blob
        const audioBlob = new Blob(chunks, { type: selectedMimeType });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Capture current text values to use in the setTimeout callback
        const currentIntroText = introText;
        const currentOutroText = outroText;

        // Show saving indicator in the input field first (not the save button)
        if (sectionToUpdate === 'intro') {
          setIsIntroAudioSaving(true);

          // After a short delay, update with the new audio and clear text
          setTimeout(() => {
            // CRITICAL FIX: Double-check the actual recording duration from the global variable
            // This ensures we don't assign audio URLs for recordings over 60 seconds
            const actualDuration = (window as any)._lastRecordingDuration || 0;

            // Check if the recording is too long - if so, don't create or assign the audio URL
            if (isRecordingTooLong || actualDuration > 60) {

              // End the saving animation
              setIsIntroAudioSaving(false);
              return;
            }

            // Only clear text for this section if we have a valid recording
            if (audioBlob.size > 0) {
              setIntroText('');

              // Make sure we don't accidentally clear the outro text
              if (outroText === '' && currentOutroText !== '') {
                setOutroText(currentOutroText);
              }
            }

            // Set the new audio URL
            setIntroAudioUrl(audioUrl);

            // End the saving animation
            setIsIntroAudioSaving(false);

            // Clear any error state since we have a valid recording
            setRecordingError(null);
            setAudioErrorSection(null);

            // Update change tracking
            setHasIntroChanges(
              initialIntroAudioUrl !== audioUrl ||
              initialIntroText !== '' ||
              initialSelectedIntroPhoto !== selectedIntroPhoto
            );
          }, 1500);
        } else if (sectionToUpdate === 'outro') {
          setIsOutroAudioSaving(true);

          // After a short delay, update with the new audio and clear text
          setTimeout(() => {
            // CRITICAL FIX: Double-check the actual recording duration from the global variable
            // This ensures we don't assign audio URLs for recordings over 60 seconds
            const actualDuration = (window as any)._lastRecordingDuration || 0;

            // Check if the recording is too long - if so, don't create or assign the audio URL
            if (isRecordingTooLong || actualDuration > 60) {

              // End the saving animation
              setIsOutroAudioSaving(false);
              return;
            }

            // Only clear text for this section if we have a valid recording
            if (audioBlob.size > 0) {
              setOutroText('');

              // Make sure we don't accidentally clear the intro text
              if (introText === '' && currentIntroText !== '') {
                setIntroText(currentIntroText);
              }
            }

            // Set the new audio URL
            setOutroAudioUrl(audioUrl);

            // End the saving animation
            setIsOutroAudioSaving(false);

            // Clear any error state since we have a valid recording
            setRecordingError(null);
            setAudioErrorSection(null);

            // Update change tracking
            setHasOutroChanges(
              initialOutroAudioUrl !== audioUrl ||
              initialOutroText !== '' ||
              initialSelectedOutroPhoto !== selectedOutroPhoto
            );
          }, 1500);
        }

        // Stop all audio tracks
        if (audioStream) {
          audioStream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        }
      };

      // Set recording state
      setIsRecording(true);

      // Set section-specific recording state - commented out since we now have the animation in the popup
      // if (section === 'intro') {
      //   setIsIntroRecording(true);
      //   setIsOutroRecording(false);
      // } else if (section === 'outro') {
      //   setIsOutroRecording(true);
      //   setIsIntroRecording(false);
      // }

      // Stop any existing timer first
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Start recorder before timer to ensure everything is ready
      recorder.start();

      // IMPORTANT: We need a small delay to ensure React state updates have propagated
      // This is crucial for the timer to work correctly
      setTimeout(() => {
        // Start a new timer
        timerRef.current = setInterval(() => {
          setRecordingTime(prev => {
            return prev + 1;
          });
        }, 1000);
      }, 100);

    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Unable to access microphone. Please check your browser permissions.');
    }
  };

  // Stop recording audio
  const stopRecording = () => {
    // Store the current section before clearing it
    const currentSection = recordingFor;

    // Capture the current recording time in a local variable to ensure it doesn't change
    const currentRecordingTime = recordingTime;

    // CRITICAL FIX: Store the current recording time in a global variable
    // that the onstop handler can access, since the state might be reset
    // before the onstop handler runs
    (window as any)._lastRecordingDuration = currentRecordingTime;
    (window as any)._lastRecordingSection = currentSection;

    // Ensure the recorder exists and is active
    if (audioRecorder && audioRecorder.state !== 'inactive') {
      try {
        // Save the current recording time as duration
        setAudioDuration(currentRecordingTime);

        // Also save to the section-specific duration
        if (currentSection === 'intro') {
          setIntroAudioDuration(currentRecordingTime);
        } else if (currentSection === 'outro') {
          setOutroAudioDuration(currentRecordingTime);
        }

        // Stop the timer first
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // Stop the recorder - this will trigger the onstop handler we defined in startRecording
        // The onstop handler will check if the recording is too long and handle it appropriately
        audioRecorder.stop();

        // Explicitly stop all tracks in the audio stream to ensure browser recording indicator stops
        if (audioStream) {
          audioStream.getTracks().forEach(track => {
            track.stop();
          });
          setAudioStream(null);
        }

        // Make sure we set the recording state to false
        setIsRecording(false);
        // Commented out since we now have the animation in the popup
        // setIsIntroRecording(false);
        // setIsOutroRecording(false);

        // The saving animation and setting the new audio URL is now handled in the recorder.onstop handler

        // Clear recording section and close popup
        setRecordingFor(null);
        setShowRecordingPopup(false);

      } catch (error) {
        console.error('Error stopping recording:', error);

        // Even if there's an error, try to stop all tracks
        if (audioStream) {
          try {
            audioStream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          } catch (e) {
            console.error('Error stopping audio tracks after error:', e);
          }
        }
      }
    } else {
      // Clean up anyway
      setIsRecording(false);
      // Commented out since we now have the animation in the popup
      // setIsIntroRecording(false);
      // setIsOutroRecording(false);

      setRecordingFor(null);

      // Close the recording popup
      setShowRecordingPopup(false);

      // Still try to stop any active tracks
      if (audioStream) {
        try {
          audioStream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        } catch (e) {
          console.error('Error stopping audio tracks in cleanup:', e);
        }
      }

      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // Play recorded audio with enhanced error handling
  const playRecording = (section: 'intro' | 'outro') => {
    // Initialize audio on first interaction
    initializeAudio();
    if (audioContext?.state === 'suspended') {
      audioContext.resume().catch(e => console.error('[playRecording] Error resuming suspended AudioContext:', e));
    }

    const audioUrl = section === 'intro' ? introAudioUrl : outroAudioUrl;
    if (!audioUrl) {
      return;
    }

    // Check if already playing - just stop if that's the case
    if (isPlaying === section) {
      stopPlayback();
      return;
    }

    // Stop any existing playback
    stopPlayback();

    const audio = new Audio();
    audioRef.current = audio; // Store ref immediately

    // Set state before playing
    setIsPlaying(section);
    setPlaybackTime(0);
    setPlaybackProgress(0);

    // Setup event listeners
    audio.addEventListener('timeupdate', () => {
      if (!audioRef.current) return; // Check ref
      const currentTime = audioRef.current.currentTime;
      const duration = audioRef.current.duration || audioDuration;
      setPlaybackTime(Math.floor(currentTime));
      setPlaybackProgress(Math.min(100, (currentTime / duration) * 100));
    });

    audio.addEventListener('loadedmetadata', () => {
      if (!audioRef.current) return;
      const duration = Math.floor(audioRef.current.duration);
      setAudioDuration(duration);

      // Also update the section-specific duration
      if (section === 'intro') {
        setIntroAudioDuration(duration);
      } else if (section === 'outro') {
        setOutroAudioDuration(duration);
      }
    });

    audio.addEventListener('ended', () => {
      stopPlayback(); // Use centralized stop function on end
    });

    audio.addEventListener('error', () => {
      stopPlayback(); // Also stop on error
    });

    // Set source
    audio.src = audioUrl;

    // Load the audio
    audio.load();

    // Play immediately
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise
        .catch(err => {
          console.error('[playRecording] Play promise rejected:', err);
          // Attempting to stop playback cleanly on error
          stopPlayback();
        });
    }
    // If no promise, playback might have started synchronously (older browsers)
    // or failed silently. State should reflect this eventually via events.
  };

  // Stop audio playback with enhanced cleanup
  const stopPlayback = () => {
    // Clean up audio element
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.src = '';
      audioRef.current.load(); // Force cleanup
    }

    // Clean up old audio player if it exists
    if (audioPlayer) {
      audioPlayer.pause();
      audioPlayer.src = '';
    }

    // Reset state
    setIsPlaying(null);
    setPlaybackProgress(0);
    setPlaybackTime(0);
    audioRef.current = null;
  };

  // Handle text input for intro/outro
  const handleTextChange = (section: 'intro' | 'outro', value: string) => {
    if (section === 'intro') {
      setIntroText(value);
      // Clear audio when text is set for this section only
      if (value.trim() !== '' && introAudioUrl) {
        URL.revokeObjectURL(introAudioUrl);
        setIntroAudioUrl(null);
      }
      // Update change tracking
      setHasIntroChanges(
        value !== initialIntroText ||
        (introAudioUrl !== initialIntroAudioUrl) ||
        (selectedIntroPhoto !== initialSelectedIntroPhoto)
      );
    } else if (section === 'outro') {
      setOutroText(value);
      // Clear audio when text is set for this section only
      if (value.trim() !== '' && outroAudioUrl) {
        URL.revokeObjectURL(outroAudioUrl);
        setOutroAudioUrl(null);
      }
      // Update change tracking
      setHasOutroChanges(
        value !== initialOutroText ||
        (outroAudioUrl !== initialOutroAudioUrl) ||
        (selectedOutroPhoto !== initialSelectedOutroPhoto)
      );
    }
  };

  // Show save confirmation popup
  const showSaveConfirmationPopup = (section: 'intro' | 'outro') => {
    // Check if there's valid content before showing the confirmation
    const hasValidContent = section === 'intro' ? hasValidIntroContent() : hasValidOutroContent();
    const hasChanges = section === 'intro' ? hasIntroChanges : hasOutroChanges;

    if (!hasValidContent) {
      console.warn(`Cannot save ${section} without text or audio content`);
      return;
    }

    if (!hasChanges) {
      console.warn(`No changes to save for ${section}`);
      return;
    }

    setConfirmationSection(section);
    setShowSaveConfirmation(true);
  };

  // Check if intro has changes
  const checkIntroChanges = (): boolean => {
    // Check text changes
    if (introText !== initialIntroText) return true;

    // Check audio changes (one exists and the other doesn't, or they're different)
    if ((introAudioUrl && !initialIntroAudioUrl) || (!introAudioUrl && initialIntroAudioUrl)) return true;

    // Check photo changes
    const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
    const currentPhotoId = selectedIntroPhoto?.photo_id;
    if ((initialPhotoId && !currentPhotoId) || (!initialPhotoId && currentPhotoId) ||
        (initialPhotoId && currentPhotoId && initialPhotoId !== currentPhotoId)) return true;

    return false;
  };

  // Check if outro has changes
  const checkOutroChanges = (): boolean => {
    // Check text changes
    if (outroText !== initialOutroText) return true;

    // Check audio changes (one exists and the other doesn't, or they're different)
    if ((outroAudioUrl && !initialOutroAudioUrl) || (!outroAudioUrl && initialOutroAudioUrl)) return true;

    // Check photo changes
    const initialPhotoId = initialSelectedOutroPhoto?.photo_id;
    const currentPhotoId = selectedOutroPhoto?.photo_id;
    if ((initialPhotoId && !currentPhotoId) || (!initialPhotoId && currentPhotoId) ||
        (initialPhotoId && currentPhotoId && initialPhotoId !== currentPhotoId)) return true;

    return false;
  };

  // Check if intro has valid content (text or audio)
  const hasValidIntroContent = (): boolean => {
    return (introText.trim() !== '' || introAudioUrl !== null);
  };

  // Check if outro has valid content (text or audio)
  const hasValidOutroContent = (): boolean => {
    return (outroText.trim() !== '' || outroAudioUrl !== null);
  };

  // Reset intro to initial state
  const resetIntroToInitial = () => {
    // First, clear any recording error state
    setRecordingError(null);
    setAudioErrorSection(null);

    // Reset text to initial value
    setIntroText(initialIntroText);

    // If we have a current audio URL that's different from the initial one, revoke it
    if (introAudioUrl && introAudioUrl !== initialIntroAudioUrl) {
      URL.revokeObjectURL(introAudioUrl);
    }

    // Set back to initial audio URL and duration
    setIntroAudioUrl(initialIntroAudioUrl);
    setIntroAudioDuration(initialIntroAudioDuration);
    setSelectedIntroPhoto(initialSelectedIntroPhoto);
    setIntroSearchQuery(''); // Reset search query
    setHasIntroChanges(false);

  };

  // Reset outro to initial state
  const resetOutroToInitial = () => {
    // First, clear any recording error state
    setRecordingError(null);
    setAudioErrorSection(null);

    // Reset text to initial value
    setOutroText(initialOutroText);

    // If we have a current audio URL that's different from the initial one, revoke it
    if (outroAudioUrl && outroAudioUrl !== initialOutroAudioUrl) {
      URL.revokeObjectURL(outroAudioUrl);
    }

    // Set back to initial audio URL and duration
    setOutroAudioUrl(initialOutroAudioUrl);
    setOutroAudioDuration(initialOutroAudioDuration);
    setSelectedOutroPhoto(initialSelectedOutroPhoto);
    setOutroSearchQuery(''); // Reset search query
    setHasOutroChanges(false);

  };

  // Handle cancel edit
  const handleCancelEdit = (section: 'intro' | 'outro') => {
    const hasChanges = section === 'intro' ? checkIntroChanges() : checkOutroChanges();

    if (hasChanges) {
      // Show confirmation dialog
      setConfirmationSection(section);
      setShowCancelConfirmation(true);
    } else {
      // No changes, just exit edit mode
      if (section === 'intro') {
        setIsIntroEditing(false);
      } else {
        setIsOutroEditing(false);
      }
    }
  };

  // Confirm cancel with reset
  const confirmCancel = () => {

    // First, stop any playback
    if (isPlaying) {
      stopPlayback();
    }

    // Check if there was a recording that was too long
    // Use both the global flag, the section-specific flags, and the global variables
    const wasRecordingTooLong = isRecordingTooLong ||
                               introRecordingTooLong ||
                               outroRecordingTooLong ||
                               ((window as any)._lastRecordingDuration || 0) > 60 ||
                               (window as any)._introRecordingTooLong === true ||
                               (window as any)._outroRecordingTooLong === true;

    // Also check section-specific global variables
    const introTooLong = introRecordingTooLong || (window as any)._introRecordingTooLong === true;
    const outroTooLong = outroRecordingTooLong || (window as any)._outroRecordingTooLong === true;


    // Reset the appropriate section
    if (confirmationSection === 'intro') {

      // If there was a temporary audio URL created, revoke it
      if (introAudioUrl && introAudioUrl !== initialIntroAudioUrl) {
        URL.revokeObjectURL(introAudioUrl);
      }

      // CRITICAL FIX: If we had a recording that was too long for this section, make sure we restore the initial audio URL
      if ((introTooLong || (audioErrorSection === 'intro' && wasRecordingTooLong))) {
        setIntroAudioUrl(initialIntroAudioUrl);
        setIntroAudioDuration(initialIntroAudioDuration);
        setIntroText(initialIntroText);
      } else {
        // Otherwise, use the normal reset function
        resetIntroToInitial();
      }

      setIsIntroEditing(false);
    } else if (confirmationSection === 'outro') {

      // If there was a temporary audio URL created, revoke it
      if (outroAudioUrl && outroAudioUrl !== initialOutroAudioUrl) {
        URL.revokeObjectURL(outroAudioUrl);
      }

      // CRITICAL FIX: If we had a recording that was too long for this section, make sure we restore the initial audio URL
      if ((outroTooLong || (audioErrorSection === 'outro' && wasRecordingTooLong))) {
        setOutroAudioUrl(initialOutroAudioUrl);
        setOutroAudioDuration(initialOutroAudioDuration);
        setOutroText(initialOutroText);
      } else {
        // Otherwise, use the normal reset function
        resetOutroToInitial();
      }

      setIsOutroEditing(false);
    }

    // Close any open audio error popup
    setShowAudioErrorPopup(false);

    // Clear any recording error state
    setRecordingError(null);
    setAudioErrorSection(null);

    // Reset the recording too long flags
    setIsRecordingTooLong(false);

    // CRITICAL FIX: Also reset the section-specific recording too long flags
    setIntroRecordingTooLong(false);
    setOutroRecordingTooLong(false);

    // Clear the global variables
    (window as any)._lastRecordingDuration = 0;
    (window as any)._lastRecordingSection = null;
    (window as any)._introRecordingTooLong = false;
    (window as any)._outroRecordingTooLong = false;

    // Close the confirmation dialog
    setShowCancelConfirmation(false);
    setConfirmationSection(null);
  };

  // Show audio deletion confirmation
  const confirmDeleteAudio = (section: 'intro' | 'outro') => {
    setAudioToDelete(section);
    setShowDeleteAudioConfirmation(true);
  };

  // Delete audio after confirmation
  const deleteAudio = () => {

    if (audioToDelete === 'intro' && introAudioUrl) {

      URL.revokeObjectURL(introAudioUrl);
      setIntroAudioUrl(null);
      setRecordingError(null);
      setIntroText('');

      // Update change tracking
      setHasIntroChanges(
        initialIntroAudioUrl !== null ||
        initialIntroText !== '' ||
        initialSelectedIntroPhoto !== selectedIntroPhoto
      );
    } else if (audioToDelete === 'outro' && outroAudioUrl) {

      URL.revokeObjectURL(outroAudioUrl);
      setOutroAudioUrl(null);
      setRecordingError(null);
      setOutroText('');

      // Update change tracking
      setHasOutroChanges(
        initialOutroAudioUrl !== null ||
        initialOutroText !== '' ||
        initialSelectedOutroPhoto !== selectedOutroPhoto
      );
    }

    // Close confirmation dialog
    setShowDeleteAudioConfirmation(false);
    setAudioToDelete(null);
  };

  // Function to handle closing the audio error popup
  const handleCloseAudioErrorPopup = () => {

    // When closing the error popup, we want to restore the original content
    // instead of clearing everything

    if (audioErrorSection === 'intro') {

      // CRITICAL FIX: Check if the recording was too long
      const wasRecordingTooLong = isRecordingTooLong || ((window as any)._lastRecordingDuration || 0) > 60;

      // If we're in edit mode, handle differently based on whether the recording was too long
      if (isIntroEditing) {

        // If the recording was too long, we should NOT restore any audio URL
        if (wasRecordingTooLong) {

          // If there's any temporary audio URL, revoke it
          if (introAudioUrl) {
            URL.revokeObjectURL(introAudioUrl);
            setIntroAudioUrl(null); // Explicitly set to null to ensure no audio URL is assigned
          }

          // Restore only the text, not the audio URL
          setIntroText(initialIntroText);
        } else {
          // If the recording was not too long, restore to the initial values

          // If there was a temporary audio URL created, revoke it
          if (introAudioUrl && introAudioUrl !== initialIntroAudioUrl) {
            URL.revokeObjectURL(introAudioUrl);
          }

          // Restore to initial values
          setIntroAudioUrl(initialIntroAudioUrl);
          setIntroText(initialIntroText);
        }
      } else {
        // If not in edit mode, just clear any temporary audio URL
        if (introAudioUrl && !initialIntroAudioUrl) {
          URL.revokeObjectURL(introAudioUrl);
          setIntroAudioUrl(null);
        }
      }
    } else if (audioErrorSection === 'outro') {

      // CRITICAL FIX: Check if the recording was too long
      const wasRecordingTooLong = isRecordingTooLong || ((window as any)._lastRecordingDuration || 0) > 60;

      // If we're in edit mode, handle differently based on whether the recording was too long
      if (isOutroEditing) {

        // If the recording was too long, we should NOT restore any audio URL
        if (wasRecordingTooLong) {

          // If there's any temporary audio URL, revoke it
          if (outroAudioUrl) {
            URL.revokeObjectURL(outroAudioUrl);
            setOutroAudioUrl(null); // Explicitly set to null to ensure no audio URL is assigned
          }

          // Restore only the text, not the audio URL
          setOutroText(initialOutroText);
        } else {
          // If the recording was not too long, restore to the initial values

          // If there was a temporary audio URL created, revoke it
          if (outroAudioUrl && outroAudioUrl !== initialOutroAudioUrl) {
            URL.revokeObjectURL(outroAudioUrl);
          }

          // Restore to initial values
          setOutroAudioUrl(initialOutroAudioUrl);
          setOutroText(initialOutroText);
        }
      } else {
        // If not in edit mode, just clear any temporary audio URL
        if (outroAudioUrl && !initialOutroAudioUrl) {
          URL.revokeObjectURL(outroAudioUrl);
          setOutroAudioUrl(null);
        }
      }
    }

    // Reset recording state
    setIsRecording(false);
    setRecordingFor(null);
    setShowRecordingPopup(false);

    // Reset the recording too long flags
    setIsRecordingTooLong(false);

    // CRITICAL FIX: Also reset the section-specific recording too long flags
    setIntroRecordingTooLong(false);
    setOutroRecordingTooLong(false);

    // Clear the global variables
    (window as any)._lastRecordingDuration = 0;
    (window as any)._lastRecordingSection = null;
    (window as any)._introRecordingTooLong = false;
    (window as any)._outroRecordingTooLong = false;

    // Clear the error and reset states
    setRecordingError(null);
    setAudioErrorSection(null);

    // Close the popup
    setShowAudioErrorPopup(false);
  };

  // Handle edit button click
  const handleEdit = (section: 'intro' | 'outro') => {

    // CRITICAL FIX: Reset recording too long flags when starting a new edit
    setIsRecordingTooLong(false);
    setIntroRecordingTooLong(false);
    setOutroRecordingTooLong(false);

    // Also clear the global variables
    (window as any)._lastRecordingDuration = 0;
    (window as any)._lastRecordingSection = null;
    (window as any)._introRecordingTooLong = false;
    (window as any)._outroRecordingTooLong = false;

    if (section === 'intro') {
      // Save initial state for tracking changes
      setInitialIntroText(introText);
      setInitialIntroAudioUrl(introAudioUrl);
      setInitialIntroAudioDuration(introAudioDuration);
      setInitialSelectedIntroPhoto(selectedIntroPhoto ? {...selectedIntroPhoto} : null);
      setHasIntroChanges(false);
      setIsIntroEditing(true);
    } else {
      // Save initial state for tracking changes
      setInitialOutroText(outroText);
      setInitialOutroAudioUrl(outroAudioUrl);
      setInitialOutroAudioDuration(outroAudioDuration);
      setInitialSelectedOutroPhoto(selectedOutroPhoto ? {...selectedOutroPhoto} : null);
      setHasOutroChanges(false);
      setIsOutroEditing(true);
    }
  };

  // Upload audio file to storage
  const uploadAudioToStorage = async (audioUrl: string, section: 'intro' | 'outro', clientId: string): Promise<{ audioFilePath: string, audioDuration: number } | null> => {
    try {
      // Convert the blob URL to a Blob object
      const response = await fetch(audioUrl);
      const audioBlob = await response.blob();

      if (audioBlob.size === 0) {
        console.error('Audio blob is empty');
        return null;
      }

      // Determine file extension & mime type
      const mimeType = audioBlob.type || 'application/octet-stream';
      let fileExtension = 'mp4'; // Default extension
      const mimeParts = mimeType.split('/');
      if (mimeParts.length > 1) {
        const subType = mimeParts[1].split(';')[0]; // Handle potential codecs string
        if (subType === 'mp4') fileExtension = 'mp4';
        else if (subType === 'webm') fileExtension = 'webm';
        else if (subType === 'opus') fileExtension = 'opus';
        else if (subType === 'ogg') fileExtension = 'oga'; // Changed to oga for consistency
        else fileExtension = subType; // Use subtype if known
      }

      // Create a unique file name based on client ID and section
      const chatId = section === 'intro' ? `${clientId}-1` : `${clientId}-2`;
      const uniqueFileName = `${chatId}_${uuidv4()}.${fileExtension}`;

      // Get the authenticated user's ID (auth.id) from the session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        throw new Error('User session not found. Please log in again.');
      }
      const userId = session.user.id; // This is the auth UUID

      // Use userId (auth UUID) for the storage path, not clientId
      const filePath = `${userId}/${uniqueFileName}`;
      const bucketName = 'audios';

      // Direct upload to Supabase Storage
      const { error: uploadError } = await supabase
        .storage
        .from(bucketName)
        .upload(filePath, audioBlob, {
          cacheControl: '3600',
          contentType: mimeType,
          upsert: false
        });

      if (uploadError) {
        console.error("Upload error:", uploadError);
        throw new Error(`Failed to upload audio: ${uploadError.message}`);
      }

      // Get Public URL
      const { data: urlData } = supabase
        .storage
        .from(bucketName)
        .getPublicUrl(filePath);

      if (!urlData?.publicUrl) {
        throw new Error(`Could not get public URL for ${filePath}`);
      }

      return {
        audioFilePath: filePath,
        audioDuration: audioDuration || 0
      };
    } catch (error: any) {
      console.error('Error uploading audio:', error);
      // Throw the error so it can be caught and displayed in the UI
      throw new Error(error.message || 'Failed to upload audio file');
    }
  };

  // Save intro/outro settings to welcome_chat table
  const handleSave = async (section: 'intro' | 'outro') => {
    // Close the confirmation dialog
    setShowSaveConfirmation(false);

    // Set the appropriate saving state based on the section
    if (section === 'intro') {
      setIsIntroSaving(true);
      setIsIntroEditing(false);
    } else {
      setIsOutroSaving(true);
      setIsOutroEditing(false);
    }

    // Set initial loading state
    setSaveStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(`Preparing to save ${section} message...`);

    try {
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;
      const language = clientInfo?.lang || 'en';

      if (!clientId) {
        console.error('Client ID not found while saving intro/outro');
        setUpdateMessage('Client ID not found. Please try again or contact support.');
        setSaveStatus('error');
        return;
      }

      // Generate chat_id based on section
      const chatId = section === 'intro' ? `${clientId}-1` : `${clientId}-2`;

      // Check if record exists first to get existing audio file path
      const { data: existingRecord } = await supabase
        .from('welcome_chat')
        .select('*')
        .eq('client_id', clientId)
        .eq('chat_id', chatId)
        .maybeSingle();

      // Prepare data for database update
      let updateData: any = {
        client_id: clientId,
        chat_id: chatId
      };

      // Update progress
      setUpdateProgress(20);
      setUpdateMessage('Processing data...');

      // Delete old audio file if it exists and we're replacing it or using text instead
      if (existingRecord?.audio_file_path) {
        const hasNewAudio = (section === 'intro' && introAudioUrl) || (section === 'outro' && outroAudioUrl);
        const hasText = section === 'intro' ? introText : outroText;

        // If we have new audio or text, delete the old audio file
        if (hasNewAudio || hasText) {
          try {
            setUpdateMessage('Removing old audio file...');
            const { error: deleteError } = await supabase
              .storage
              .from('audios')
              .remove([existingRecord.audio_file_path]);

            if (deleteError) {
              console.error('Error deleting old audio file:', deleteError);
              // Continue with the save process even if delete fails
            } else {

            }
          } catch (deleteError) {
            console.error('Error during old audio deletion:', deleteError);
            // Continue with the save process even if delete fails
          }
        }
      }

      // Handle audio upload if exists
      let audioUploadResult = null;
      if ((section === 'intro' && introAudioUrl) || (section === 'outro' && outroAudioUrl)) {
        setUpdateMessage('Uploading audio file...');
        setUpdateProgress(30);

        try {
          if (section === 'intro' && introAudioUrl) {
            audioUploadResult = await uploadAudioToStorage(introAudioUrl, 'intro', clientId);
          } else if (section === 'outro' && outroAudioUrl) {
            audioUploadResult = await uploadAudioToStorage(outroAudioUrl, 'outro', clientId);
          }

          if (!audioUploadResult) {
            throw new Error('Failed to upload audio file');
          }
        } catch (uploadError: any) {
          console.error('Audio upload error:', uploadError);
          setUpdateMessage(uploadError.message || 'Failed to upload audio file');
          setSaveStatus('error');
          throw uploadError; // Re-throw to stop the save process
        }

        setUpdateProgress(60);
        setUpdateMessage('Audio uploaded successfully');
      } else {
        setUpdateProgress(40);
      }

      // Set text based on language
      if (language === 'kh') {
        updateData.answer_kh = section === 'intro' ? introText : outroText;
        // If we're in Khmer mode, set English answer to empty to avoid conflicts
        updateData.answer = '';
      } else {
        updateData.answer = section === 'intro' ? introText : outroText;
        // If we're in English mode, set Khmer answer to empty to avoid conflicts
        updateData.answer_kh = '';
      }

      // Add photo data if selected
      if (section === 'intro' && selectedIntroPhoto) {
        updateData.photo_url = selectedIntroPhoto.full_photo_urls;
        updateData.photo_id = selectedIntroPhoto.photo_id;
      } else if (section === 'outro' && selectedOutroPhoto) {
        updateData.photo_url = selectedOutroPhoto.full_photo_urls;
        updateData.photo_id = selectedOutroPhoto.photo_id;
      }

      // Add audio data if uploaded successfully
      if (audioUploadResult) {
        // Get the public URL directly from Supabase
        const { data: urlData } = supabase
          .storage
          .from('audios')
          .getPublicUrl(audioUploadResult.audioFilePath);

        updateData.audio_url = urlData?.publicUrl || '';
        updateData.audio_duration = audioUploadResult.audioDuration;
        updateData.audio_file_path = audioUploadResult.audioFilePath;
        updateData.answer = '';
        updateData.answer_kh = '';

        // // If audio exists, text should be empty
        // if (language === 'kh') {
        //   updateData.answer_kh = '';
        // } else {
        //   updateData.answer = '';
        // }
      } else if ((section === 'intro' ? introText : outroText).trim() !== '') {
        // If we have text but no audio, make sure audio fields are cleared
        updateData.audio_url = null;
        updateData.audio_duration = null;
        updateData.audio_file_path = null;
      }

      setUpdateProgress(70);
      setUpdateMessage('Saving to database...');

      // Check if record still exists (it might have changed since we checked earlier)
      setUpdateProgress(80);

      if (existingRecord) {
        // Record exists, update it
        setUpdateMessage('Updating existing record...');
        try {
          const { error } = await supabase
            .from('welcome_chat')
            .update(updateData)
            .eq('client_id', clientId)
            .eq('chat_id', chatId);

          if (error) throw error;
        } catch (error) {
          console.error('Error updating record:', error);
          throw new Error('Failed to update existing record');
        }

      } else {
        // Record doesn't exist, insert new record
        setUpdateMessage('Creating new record...');
        try {
          const { error } = await supabase
            .from('welcome_chat')
            .insert([updateData]);

          if (error) throw error;
        } catch (error) {
          console.error('Error inserting record:', error);
          throw new Error('Failed to create new record');
        }
      }

      setUpdateProgress(90);
      setUpdateMessage(`${section === 'intro' ? 'Intro' : 'Outro'} message saved successfully!`);

      // Trigger webhook for all updates (text, audio, and photo changes)
      try {
        // Get sector from client info
        const sector = clientInfo?.sector || null;

        // Find the original data to compare changes
        const originalPhoto = section === 'intro' ? initialSelectedIntroPhoto : initialSelectedOutroPhoto;
        const currentPhoto = section === 'intro' ? selectedIntroPhoto : selectedOutroPhoto;

        // Determine if photo has changed
        const hasPhotoChanged =
          // Photo added (didn't exist before but exists now)
          (currentPhoto && !originalPhoto) ||
          // Photo removed (existed before but not now)
          (!currentPhoto && originalPhoto) ||
          // Photo changed (different photo ID)
          (currentPhoto && originalPhoto &&
           originalPhoto.photo_id !== currentPhoto.photo_id);

        // Get current text and audio URL
        const currentText = section === 'intro' ? introText : outroText;
        const audioUrl = section === 'intro' ?
          (updateData.audio_url || "") :
          (updateData.audio_url || "");

        // Prepare webhook data
        const welcomeData = {
          chat_id: chatId,
          client_id: clientId,
          answer: currentText,
          lang: language,
          sector,
          audio_url: audioUrl,
          is_photo: hasPhotoChanged
        };

        // Send webhook for all updates
        await sendWelcomeUpdateWebhook(welcomeData);
        setUpdateProgress(100);
        setUpdateMessage(`${section === 'intro' ? 'Intro' : 'Outro'} message saved!`);
      } catch (webhookError) {
        console.error('Error sending welcome webhook:', webhookError);
        // Don't fail the save operation if webhook fails
        setUpdateProgress(100);
      }

      // Set success status
      setSaveStatus('success');

    } catch (error: any) {
      console.error('Error saving intro/outro:', error);
      setUpdateMessage(error.message || `Error saving ${section} message. Please try again.`);
      setSaveStatus('error');
    } finally {
      // Reset the appropriate saving state based on the section
      setTimeout(() => {
        if (section === 'intro') {
          setIsIntroSaving(false);
        } else {
          setIsOutroSaving(false);
        }

        // Reset save status after a delay
        setTimeout(() => {
          setSaveStatus('idle');
        }, 1500);
      }, 1000);
    }
  };

  // Load existing intro/outro data
  const loadIntroOutroData = async () => {
    try {
      const clientInfo = getClientInfo();
      const clientId = clientInfo?.client_id;
      const language = clientInfo?.lang || 'en';

      if (!clientId) {
        console.error('Client ID not found while loading intro/outro data');
        return;
      }

      // Generate chat_ids for intro and outro
      const introId = `${clientId}-1`;
      const outroId = `${clientId}-2`;

      // Fetch intro data
      const { data: introData, error: introError } = await supabase
        .from('welcome_chat')
        .select('*')
        .eq('client_id', clientId)
        .eq('chat_id', introId)
        .maybeSingle();

      if (introError) {
        console.error('Error fetching intro data:', introError);
      }

      // Fetch outro data
      const { data: outroData, error: outroError } = await supabase
        .from('welcome_chat')
        .select('*')
        .eq('client_id', clientId)
        .eq('chat_id', outroId)
        .maybeSingle();

      if (outroError) {
        console.error('Error fetching outro data:', outroError);
      }

      // Set intro data if exists
      if (introData) {
        let introTextValue = '';

        // Set text based on language
        if (language === 'kh' && introData.answer_kh) {
          introTextValue = introData.answer_kh;
          setIntroText(introTextValue);
        } else if (introData.answer) {
          introTextValue = introData.answer;
          setIntroText(introTextValue);
        }

        // Initialize initial text value
        setInitialIntroText(introTextValue);

        // Set audio URL if exists
        if (introData.audio_url) {
          setIntroAudioUrl(introData.audio_url);
          setInitialIntroAudioUrl(introData.audio_url);
          // Store the audio duration for this specific audio
          const duration = introData.audio_duration || 0;
          setIntroAudioDuration(duration);
          setInitialIntroAudioDuration(duration);
        }

        // Set photo if exists
        if (introData.photo_url && introData.photo_id) {
          const photoData = {
            id: 0, // We don't need the actual ID here
            photo_id: introData.photo_id,
            photo_url: Array.isArray(introData.photo_url) && introData.photo_url.length > 0
              ? introData.photo_url[0]
              : null,
            full_photo_urls: introData.photo_url
          };

          setSelectedIntroPhoto(photoData);
          setInitialSelectedIntroPhoto({...photoData});
        }
      }

      // Set outro data if exists
      if (outroData) {
        let outroTextValue = '';

        // Set text based on language
        if (language === 'kh' && outroData.answer_kh) {
          outroTextValue = outroData.answer_kh;
          setOutroText(outroTextValue);
        } else if (outroData.answer) {
          outroTextValue = outroData.answer;
          setOutroText(outroTextValue);
        }

        // Initialize initial text value
        setInitialOutroText(outroTextValue);

        // Set audio URL if exists
        if (outroData.audio_url) {
          setOutroAudioUrl(outroData.audio_url);
          setInitialOutroAudioUrl(outroData.audio_url);
          // Store the audio duration for this specific audio
          const duration = outroData.audio_duration || 0;
          setOutroAudioDuration(duration);
          setInitialOutroAudioDuration(duration);
        }

        // Set photo if exists
        if (outroData.photo_url && outroData.photo_id) {
          const photoData = {
            id: 0, // We don't need the actual ID here
            photo_id: outroData.photo_id,
            photo_url: Array.isArray(outroData.photo_url) && outroData.photo_url.length > 0
              ? outroData.photo_url[0]
              : null,
            full_photo_urls: outroData.photo_url
          };

          setSelectedOutroPhoto(photoData);
          setInitialSelectedOutroPhoto({...photoData});
        }
      }

      // Reset change tracking
      setHasIntroChanges(false);
      setHasOutroChanges(false);

    } catch (error) {
      console.error('Error loading intro/outro data:', error);
    }
  };

  // Load initial data
  useEffect(() => {
    const initializeData = async () => {
      setIsLoadingCount(true);
      try {
        await Promise.all([
          fetchFaqCount(),
          fetchPhotoCount(),
          loadIntroOutroData(),
          fetchAllPhotos() // Add this to fetch all photos on page load
        ]);
      } catch (error) {
        console.error('Error initializing data:', error);
      } finally {
        setIsLoadingCount(false);
      }
    };

    initializeData();
  }, []);

  // Update dropdown position when showing results - keep it attached to search input
  useEffect(() => {
    if (showIntroResults && introSearchInputRef.current) {
      const updatePosition = () => {
        const rect = introSearchInputRef.current!.getBoundingClientRect();
        setIntroDropdownPosition({
          // Use viewport coordinates to keep dropdown attached to input when scrolling
          top: rect.bottom,
          left: rect.left,
          width: rect.width
        });
      };

      updatePosition();

      // Update position on scroll and resize to keep dropdown attached to input
      window.addEventListener('scroll', updatePosition, { passive: true });
      window.addEventListener('resize', updatePosition);

      // Add touchmove event for better mobile support
      document.addEventListener('touchmove', updatePosition, { passive: true });

      // Use requestAnimationFrame for smoother updates during scroll
      let ticking = false;
      const scrollHandler = () => {
        if (!ticking) {
          window.requestAnimationFrame(() => {
            updatePosition();
            ticking = false;
          });
          ticking = true;
        }
      };
      window.addEventListener('scroll', scrollHandler, { passive: true });

      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
        document.removeEventListener('touchmove', updatePosition);
        window.removeEventListener('scroll', scrollHandler);
      };
    }
  }, [showIntroResults]);

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        introSearchInputRef.current &&
        !introSearchInputRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest('.photo-search-dropdown')
      ) {
        setShowIntroResults(false);
      }
      if (outroSearchResultsRef.current && !outroSearchResultsRef.current.contains(event.target as Node)) {
        setShowOutroResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Effect to initialize audio on first user interaction (if not already initialized)
  useEffect(() => {
    const initAudioOnInteraction = () => {
      if (!audioInitialized) {
        initializeAudio();
        // Remove the listener after the first interaction
        window.removeEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
        window.removeEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
      }
    };

    // Add listeners for the first user interaction
    // Using { once: true } to ensure it only runs once
    if (typeof window !== 'undefined' && !audioInitialized) {
       window.addEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
       window.addEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
    }

    // Cleanup listeners on component unmount
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('touchstart', initAudioOnInteraction);
        window.removeEventListener('mousedown', initAudioOnInteraction);
      }
    };
  }, [audioInitialized]);

  // Effect to auto-focus textarea when editing popup appears
  useEffect(() => {
    // We need to track if this is the initial appearance of the popup
    if (editingItem && textareaRef.current) {
      // Use a small timeout to ensure the DOM is fully updated
      setTimeout(() => {
        if (textareaRef.current) {
          // Focus the textarea
          textareaRef.current.focus();

          // Place cursor at the end of the text
          const length = textareaRef.current.value.length;
          textareaRef.current.setSelectionRange(length, length);
        }
      }, 10);
    }
  // Only run this effect when the popup appears/disappears, not on every edit
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editingItem !== null]);

  // Add document visibility change listener
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPlayback();
        // Stop recording when tab becomes inactive to prevent browser recording indicator from staying on
        if (isRecording) {
          stopRecording();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isRecording]);

  // Update change tracking whenever relevant data changes
  useEffect(() => {
    if (isIntroEditing) {
      setHasIntroChanges(checkIntroChanges());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [introText, introAudioUrl, selectedIntroPhoto, isIntroEditing, initialIntroText, initialIntroAudioUrl, initialSelectedIntroPhoto]);

  useEffect(() => {
    if (isOutroEditing) {
      setHasOutroChanges(checkOutroChanges());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [outroText, outroAudioUrl, selectedOutroPhoto, isOutroEditing, initialOutroText, initialOutroAudioUrl, initialSelectedOutroPhoto]);

  // Add debug logging for content validation
  useEffect(() => {
    if (isIntroEditing) {
      const hasContent = hasValidIntroContent();
      const hasChanges = hasIntroChanges;
      console.debug(`Intro - Has content: ${hasContent}, Has changes: ${hasChanges}, Save button enabled: ${hasContent && hasChanges}`);
    }
  }, [introText, introAudioUrl, hasIntroChanges, isIntroEditing]);

  useEffect(() => {
    if (isOutroEditing) {
      const hasContent = hasValidOutroContent();
      const hasChanges = hasOutroChanges;
      console.debug(`Outro - Has content: ${hasContent}, Has changes: ${hasChanges}, Save button enabled: ${hasContent && hasChanges}`);
    }
  }, [outroText, outroAudioUrl, hasOutroChanges, isOutroEditing]);

  // Cleanup effect to ensure all tracks are stopped when component unmounts
  useEffect(() => {
    return () => {
      // Stop any active recording
      if (isRecording) {
        try {
          if (audioRecorder && audioRecorder.state !== 'inactive') {
            audioRecorder.stop();
          }
        } catch (e) {
          console.error('Error stopping recorder during cleanup:', e);
        }
      }

      // Stop any active audio tracks
      if (audioStream) {
        try {
          audioStream.getTracks().forEach(track => track.stop());
        } catch (e) {
          console.error('Error stopping audio tracks during cleanup:', e);
        }
      }

      // Clear any active timers
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isRecording, audioRecorder, audioStream]);

  // Close image gallery when clicking outside or pressing escape
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (imageGalleryRef.current && !imageGalleryRef.current.contains(event.target as Node)) {
        setImageGallery(null);
      }
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'Escape') {
        if (isZoomed) {
          // If zoomed, first reset zoom rather than closing the gallery
          setIsZoomed(false);
        } else {
          // If not zoomed, then close the gallery
          setImageGallery(null);
        }
      } else if (event.key === 'ArrowLeft') {
        showPreviousImage();
      } else if (event.key === 'ArrowRight') {
        showNextImage();
      }
    }

    if (imageGallery) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [imageGallery, isZoomed, showPreviousImage, showNextImage]);

  return (
    <div className="min-h-screen bg-deep-blue pb-16">
      <div className="flex-grow container mx-auto px-4 pt-4 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Top navigation-like row with logo */}
          <div className="flex justify-center py-2 mb-8">
            <Link href="/dashboard">
              <img
                src="/images/white_tran_logo.svg"
                alt="Chhlat Logo"
                className="h-10 w-auto cursor-pointer hover:opacity-80 transition-opacity"
              />
            </Link>
          </div>

          {/* Content header with title and back button */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-2">
              <Link href="/dashboard" className="text-sm text-zinc-400 hover:text-white">
                ← {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('intros_outros')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div className="rounded-xl p-3 sm:p-6" style={{
              // boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '0.75rem',
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            }}>
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Stats</h2> */}

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - (totalFaqs / (totalFaqsLimit || 1)))}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center" style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('brain')}</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{totalFaqs} <span className="text-zinc-400">/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Photo Gallery Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - (photoCount / (photoLimit || 1)))}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center" style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('photos')}</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{photoCount} <span className="text-zinc-400">/ {photoLimit || 0}</span></>
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div className="rounded-xl p-3 sm:p-6" style={{
              // boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 20px rgba(255, 255, 255, 0.15)'
              backgroundColor: 'rgba(255, 255, 255, 0.025)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '0.75rem',
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            }}>
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Management</h2> */}

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <Link
                  href="/dashboard/knowledge"
                  className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  style={{
                    boxShadow: '0 0 10px rgba(255, 255, 255, 0.1)',
                  }}
                >
                  {t('business_insight')}
                </Link>
                <Link
                  href="/dashboard/knowledge/photo"
                  className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  style={{
                    boxShadow: '0 0 10px rgba(255, 255, 255, 0.1)',
                  }}
                >
                  {t('photo_gallery')}
                </Link>
                <Link
                  href="/dashboard/knowledge/introsOutros"
                  className="bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  style={{
                    boxShadow: '0 0 15px rgba(134, 107, 255, 0.4)',
                  }}
                >
                  {t('intro_outro')}
                </Link>
              </div>
            </div>
          </div>

          {/* Intro Section */}
          <div className="rounded-xl p-6 mb-6" style={{
            // boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '0.75rem',
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
          }}>
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center">
                <FaComments className="text-jade-purple mr-3 h-5 w-5" />
                <h2 className="text-xl font-bold font-title">{t('intro_message')}</h2>
              </div>
              <div className="flex space-x-2">
                {isIntroEditing ? (
                  <>
                    <button
                      onClick={() => handleCancelEdit('intro')}
                      className="bg-black/30 text-white hover:bg-black/50 border border-white/20 hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                    >
                      {t('cancel')}
                    </button>
                    <div className="relative">
                      <button
                        onClick={() => showSaveConfirmationPopup('intro')}
                        className={`bg-jade-purple text-white hover:bg-jade-purple-dark border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base ${(!hasIntroChanges || !hasValidIntroContent()) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        style={{
                          boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                        }}
                        disabled={isIntroSaving || !hasIntroChanges || !hasValidIntroContent()}
                        title={!hasValidIntroContent() ? "Add text or audio before saving" : (!hasIntroChanges ? "No changes to save" : "Save changes")}
                      >
                        {isIntroSaving ? t('saving') : t('save')}
                      </button>
                      {isIntroEditing && !hasValidIntroContent() && (
                        <div className="absolute -bottom-6 right-0 text-xs text-red-400 whitespace-nowrap">
                          Add text or audio
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <button
                    onClick={() => handleEdit('intro')}
                    className="bg-white/5 hover:bg-white/20 text-white border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                    style={{
                      boxShadow: '0 0 10px rgba(134, 107, 255, 0.2)'
                    }}
                  >
                    {t('edit')}
                  </button>
                )}
              </div>
            </div>
            <p className="text-white/60 text-sm mb-4 md:mb-6 font-body">
              {t('intro_description')}
            </p>

            {/* Photo Search Bar */}
            <div className="mb-4 relative">
              <div className="relative" style={{ position: 'relative', isolation: 'isolate' }}>
                <input
                  type="text"
                  value={introSearchQuery}
                  onChange={handleIntroPhotoSearch}
                  onFocus={() => introSearchResults.length > 0 && setShowIntroResults(true)}
                  placeholder={t('search_photo_placeholder')}
                  disabled={!isIntroEditing}
                  className={`w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none ${isIntroEditing ? 'focus:border-jade-purple' : 'cursor-not-allowed opacity-70'}`}
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)',
                    fontSize: '16px' // Prevent auto-zoom on mobile
                  }}
                  ref={introSearchInputRef}
                  autoComplete="off" // Prevent browser autocomplete from interfering
                  spellCheck="false" // Disable spell checking
                />
                {isSearchingIntro && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Use the Portal-based dropdown component */}
              <PhotoSearchDropdown
                show={showIntroResults}
                results={introSearchResults}
                position={introDropdownPosition}
                onSelectPhoto={handleSelectIntroPhoto}
              />
            </div>

            {/* Selected Photo Display or Loading Animation */}
            {isIntroPhotoLoading ? (
              <div className="mb-4 p-3 bg-black/30 border border-white/20 rounded-lg flex items-center justify-center h-16" style={{
                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
              }}>
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-white/70 text-sm">{t('loading')}</span>
                </div>
              </div>
            ) : selectedIntroPhoto && (
              <div className="mb-4 p-3 bg-black/30 border border-white/20 rounded-lg flex items-center justify-between animate-fadeIn" style={{
                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
              }}>
                <div className="flex items-center gap-3">
                  {/* Photo Thumbnail */}
                  <div className="w-10 h-10 bg-black/30 border border-white/20 rounded overflow-hidden flex-shrink-0" style={{
                    boxShadow: 'inset 0 0 5px rgba(255, 255, 255, 0.1)'
                  }}>
                    {selectedIntroPhoto.photo_url ? (
                      <img
                        src={selectedIntroPhoto.photo_url}
                        alt={selectedIntroPhoto.photo_id}
                        className="w-full h-full object-cover cursor-pointer"
                        onClick={() => handleViewImage(selectedIntroPhoto.full_photo_urls || (selectedIntroPhoto.photo_url ? [selectedIntroPhoto.photo_url] : null))}
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-black/50 text-white/50">
                        <span>{t('no_image')}</span>
                      </div>
                    )}
                  </div>
                  {/* Photo ID */}
                  <div>
                    <p className="text-white">{selectedIntroPhoto.photo_id}</p>
                  </div>
                </div>
                {/* Remove Button */}
                <button
                  disabled={!isIntroEditing}
                  onClick={(e) => isIntroEditing && handleClearSelectedPhoto(e, 'intro')}
                  className={`p-1 rounded-full ${isIntroEditing ? 'bg-black/40 hover:bg-black/60 text-white/60 hover:text-white border border-white/20' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'}`}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Answer Input */}
            <div className="mb-4">
              <div
                className={`px-2 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none ${isIntroEditing ? 'hover:border-jade-purple cursor-pointer' : 'opacity-70'} flex items-center min-h-[42px] relative`}
                style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
                onClick={() => {
                  // Removed isIntroRecording check since we now have the animation in the popup
                  if (!isIntroEditing || isPlaying === 'intro' || introAudioUrl) {
                    return;
                  }
                  setEditingItem({
                    section: 'intro',
                    value: introText
                  });
                }}
              >
                {/* Regular input display */}
                {/* Removed isIntroRecording check since we now have the animation in the popup */}
                {!introAudioUrl && !isIntroAudioSaving && (
                  introText ?
                    <span className="truncate pr-10">{introText}</span> :
                    <span className="text-zinc-500 truncate">{t('enter_welcome_message')}</span>
                )}

                {/* Recording in progress overlay - commented out since we now have the animation in the popup */}
                {/* {isIntroRecording && (
                  <div className="absolute inset-0 bg-jade-purple/40 border border-jade-purple/70 flex items-center justify-center z-10 rounded-lg" style={{
                    boxShadow: '0 0 15px rgba(134, 107, 255, 0.3)'
                  }}>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                      <span className="text-white font-mono">{formatRecordingTime(recordingTime)}</span>
                    </div>
                  </div>
                )} */}

                {/* Saving indicator overlay */}
                {isIntroAudioSaving && (
                  <div className="absolute inset-0 border border-white/20 flex items-center justify-center z-20 rounded-lg" style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    <div className="flex items-center space-x-3">
                      <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-white font-medium">{t('saving')}</span>
                    </div>
                  </div>
                )}

                {/* Audio playback UI */}
                {introAudioUrl && !isIntroAudioSaving && (
                  // <div className={`absolute inset-0 ${isIntroEditing ? 'bg-black/40' : 'bg-black/30'} border border-white/20 flex items-center justify-between px-4 z-10 rounded-lg`} style={{
                  <div className={`absolute inset-0 border border-white/20 flex items-center justify-between px-4 z-10 rounded-lg`} style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    {/* Progress bar - Full width background */}
                    {isPlaying === 'intro' && (
                      <div className="absolute inset-0 bg-jade-purple/90 z-0 rounded-lg" style={{
                        width: `${playbackProgress}%`,
                        transition: 'width 0.3s linear'
                      }}></div>
                    )}

                    <div className="flex items-center space-x-2 flex-grow z-10">
                      {recordingError ? (
                        <span className="text-red-400 truncate">
                          Error: {recordingError}
                        </span>
                      ) : (
                        <>
                          {!recordingError && isPlaying !== 'intro' && (
                            <button
                              type="button"
                              className="mr-2 p-1 rounded-full bg-jade-purple hover:bg-jade-purple-dark text-white touch-manipulation"
                              style={{ touchAction: "manipulation" }}
                              onClick={() => playRecording('intro')}
                              onTouchEnd={(e) => {
                                e.preventDefault();
                                playRecording('intro');
                              }}
                              aria-label="Play audio"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>
                          )}
                          {isPlaying === 'intro' && (
                            <button
                              type="button"
                              className="mr-2 p-1 rounded-full bg-zinc-600 hover:bg-zinc-500 text-white"
                              onClick={stopPlayback}
                              onTouchEnd={(e) => {
                                e.preventDefault();
                                stopPlayback();
                              }}
                              aria-label="Stop audio"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <rect x="6" y="6" width="12" height="12" rx="2" />
                              </svg>
                            </button>
                          )}
                          <span className="text-white truncate">
                            {t('audio')} ({isPlaying === 'intro' ? (audioDuration - playbackTime) : introAudioDuration}s)
                          </span>
                        </>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 z-10">
                      <button
                        type="button"
                        disabled={!isIntroEditing}
                        className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full ${isIntroEditing ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-zinc-700 text-zinc-500 cursor-not-allowed'}`}
                        onClick={(e) => {
                          if (!isIntroEditing) return;
                          e.stopPropagation();
                          if (isPlaying === 'intro') {
                            stopPlayback();
                          }
                          // Show confirmation dialog instead of deleting immediately
                          confirmDeleteAudio('intro');
                        }}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}

                {/* Microphone button */}
                <button
                  type="button"
                  disabled={!isIntroEditing}
                  className={`absolute right-2 p-1.5 rounded-full transition-colors ${isIntroEditing ? 'bg-black/40 text-white/70 hover:bg-jade-purple/30 hover:text-white border border-white/20' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'}`}
                  onClick={(e) => {
                    if (!isIntroEditing) return;
                    e.stopPropagation();
                    e.preventDefault();



                    setShowRecordingPopup(true);
                    setRecordingFor('intro');
                    // Don't start recording here - wait for user to click Start Recording

                    return false;
                  }}
                  title={isIntroEditing ? "Record intro" : "Edit mode required"}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Outro Section */}
          <div className="rounded-xl p-6 mb-6" style={{
            // boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '0.75rem',
            boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
          }}>
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center">
                <FaSignOutAlt className="text-jade-purple mr-3 h-5 w-5" />
                <h2 className="text-xl font-bold font-title">{t('outro_message')}</h2>
              </div>
              <div className="flex space-x-2">
                {isOutroEditing ? (
                  <>
                    <button
                      onClick={() => handleCancelEdit('outro')}
                      className="bg-black/30 text-white hover:bg-black/50 border border-white/20 hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                    >
                      {t('cancel')}
                    </button>
                    <div className="relative">
                      <button
                        onClick={() => showSaveConfirmationPopup('outro')}
                        className={`bg-jade-purple text-white hover:bg-jade-purple-dark border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base ${(!hasOutroChanges || !hasValidOutroContent()) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        style={{
                          boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                        }}
                        disabled={isOutroSaving || !hasOutroChanges || !hasValidOutroContent()}
                        title={!hasValidOutroContent() ? "Add text or audio before saving" : (!hasOutroChanges ? "No changes to save" : "Save changes")}
                      >
                        {isOutroSaving ? t('saving') : t('save')}
                      </button>
                      {isOutroEditing && !hasValidOutroContent() && (
                        <div className="absolute -bottom-6 right-0 text-xs text-red-400 whitespace-nowrap">
                          Add text or audio
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <button
                    onClick={() => handleEdit('outro')}
                    className="bg-white/5 hover:bg-white/20 text-white border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                    style={{
                      boxShadow: '0 0 10px rgba(134, 107, 255, 0.2)'
                    }}
                  >
                    {t('edit')}
                  </button>
                )}
              </div>
            </div>
            <p className="text-white/60 text-sm mb-4 md:mb-6 font-body">
              {t('outro_description')}
            </p>

            {/* Photo Search Bar */}
            <div className="mb-4 relative">
              <div className="relative">
                <input
                  type="text"
                  value={outroSearchQuery}
                  onChange={handleOutroPhotoSearch}
                  onFocus={() => outroSearchResults.length > 0 && setShowOutroResults(true)}
                  placeholder={t('search_photo_placeholder')}
                  disabled={!isOutroEditing}
                  className={`w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none ${isOutroEditing ? 'focus:border-jade-purple' : 'cursor-not-allowed opacity-70'}`}
                  style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}
                />
                {isSearchingOutro && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Search Results Dropdown */}
              {showOutroResults && (
                <div
                  className="absolute z-50 w-full mt-1 bg-jade-purple/90 border border-white/20 rounded-lg shadow-lg max-h-60 overflow-y-auto backdrop-blur-sm"
                  ref={outroSearchResultsRef}
                  style={{
                    boxShadow: '0 0 15px rgba(255, 255, 255, 0.1)'
                  }}
                >
                  {outroSearchResults.length > 0 ? (
                    outroSearchResults.map(photo => (
                      <div
                        key={photo.id}
                        className="flex items-center gap-3 p-3 hover:bg-jade-purple-dark cursor-pointer border-b border-white/10 last:border-0"
                        onClick={() => handleSelectOutroPhoto(photo)}
                      >
                        {/* Photo Thumbnail */}
                        <div className="w-10 h-10 bg-black/30 border border-white/20 rounded overflow-hidden flex-shrink-0" style={{
                          boxShadow: 'inset 0 0 5px rgba(255, 255, 255, 0.1)'
                        }}>
                          {photo.photo_url && photo.photo_url.length > 0 ? (
                            <img
                              src={photo.photo_url[0]}
                              alt={photo.photo_id}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                              }}
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-zinc-600 text-zinc-400">
                              <span>No Image</span>
                            </div>
                          )}
                        </div>
                        {/* Photo ID */}
                        <div className="flex-1 truncate">
                          <p className="text-white truncate">{photo.photo_id}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-3 text-white/50 text-center">
                      {t('no_photos_found')}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Photo Display or Loading Animation */}
            {isOutroPhotoLoading ? (
              <div className="mb-4 p-3 bg-black/30 border border-white/20 rounded-lg flex items-center justify-center h-16" style={{
                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
              }}>
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-white/70 text-sm">{t('loading')}</span>
                </div>
              </div>
            ) : selectedOutroPhoto && (
              <div className="mb-4 p-3 bg-black/30 border border-white/20 rounded-lg flex items-center justify-between animate-fadeIn" style={{
                boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
              }}>
                <div className="flex items-center gap-3">
                  {/* Photo Thumbnail */}
                  <div className="w-10 h-10 bg-black/30 border border-white/20 rounded overflow-hidden flex-shrink-0" style={{
                    boxShadow: 'inset 0 0 5px rgba(255, 255, 255, 0.1)'
                  }}>
                    {selectedOutroPhoto.photo_url ? (
                      <img
                        src={selectedOutroPhoto.photo_url}
                        alt={selectedOutroPhoto.photo_id}
                        className="w-full h-full object-cover cursor-pointer"
                        onClick={() => handleViewImage(selectedOutroPhoto.full_photo_urls || (selectedOutroPhoto.photo_url ? [selectedOutroPhoto.photo_url] : null))}
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-black/50 text-white/50">
                        <span>{t('no_image')}</span>
                      </div>
                    )}
                  </div>
                  {/* Photo ID */}
                  <div>
                    <p className="text-white">{selectedOutroPhoto.photo_id}</p>
                  </div>
                </div>
                {/* Remove Button */}
                <button
                  disabled={!isOutroEditing}
                  onClick={(e) => isOutroEditing && handleClearSelectedPhoto(e, 'outro')}
                  className={`p-1 rounded-full ${isOutroEditing ? 'bg-black/40 hover:bg-black/60 text-white/60 hover:text-white border border-white/20' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'}`}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Answer Input */}
            <div className="mb-4">
              <div
                className={`px-2 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none ${isOutroEditing ? 'hover:border-jade-purple cursor-pointer' : 'opacity-70'} flex items-center min-h-[42px] relative`}
                style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
                onClick={() => {
                  // Removed isOutroRecording check since we now have the animation in the popup
                  if (!isOutroEditing || isPlaying === 'outro' || outroAudioUrl) {
                    return;
                  }
                  setEditingItem({
                    section: 'outro',
                    value: outroText
                  });
                }}
              >
                {/* Regular input display */}
                {/* Removed isOutroRecording check since we now have the animation in the popup */}
                {!outroAudioUrl && !isOutroAudioSaving && (
                  outroText ?
                    <span className="truncate pr-10">{outroText}</span> :
                    <span className="text-zinc-500 truncate">{t('enter_closing_message')}</span>
                )}

                {/* Recording in progress overlay - commented out since we now have the animation in the popup */}
                {/* {isOutroRecording && (
                  <div className="absolute inset-0 bg-jade-purple/40 border border-jade-purple/70 flex items-center justify-center z-10 rounded-lg" style={{
                    boxShadow: '0 0 15px rgba(134, 107, 255, 0.3)'
                  }}>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                      <span className="text-white font-mono">{formatRecordingTime(recordingTime)}</span>
                    </div>
                  </div>
                )} */}

                {/* Saving indicator overlay */}
                {isOutroAudioSaving && !isRecording && (
                  <div className="absolute inset-0 border border-white/20 flex items-center justify-center z-20 rounded-lg" style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    <div className="flex items-center space-x-3">
                      <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-white font-medium">{t('saving')}</span>
                    </div>
                  </div>
                )}

                {/* Audio playback UI */}
                {outroAudioUrl && !isOutroAudioSaving && (
                  <div className={`absolute inset-0 border border-white/20 flex items-center justify-between px-4 z-10 rounded-lg`} style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    {/* Progress bar - Full width background */}
                    {isPlaying === 'outro' && (
                      <div className="absolute inset-0 bg-jade-purple/90 z-0 rounded-lg" style={{
                        width: `${playbackProgress}%`,
                        transition: 'width 0.3s linear'
                      }}></div>
                    )}

                    <div className="flex items-center space-x-2 flex-grow z-10">
                      {recordingError ? (
                        <span className="text-red-400 truncate">
                          Error: {recordingError}
                        </span>
                      ) : (
                        <>
                          {!recordingError && isPlaying !== 'outro' && (
                            <button
                              type="button"
                              className="mr-2 p-1 rounded-full bg-jade-purple hover:bg-jade-purple-dark text-white touch-manipulation"
                              style={{ touchAction: "manipulation" }}
                              onClick={() => playRecording('outro')}
                              onTouchEnd={(e) => {
                                e.preventDefault();
                                playRecording('outro');
                              }}
                              aria-label="Play audio"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>
                          )}
                          {isPlaying === 'outro' && (
                            <button
                              type="button"
                              className="mr-2 p-1 rounded-full bg-zinc-600 hover:bg-zinc-500 text-white"
                              onClick={stopPlayback}
                              onTouchEnd={(e) => {
                                e.preventDefault();
                                stopPlayback();
                              }}
                              aria-label="Stop audio"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <rect x="6" y="6" width="12" height="12" rx="2" />
                              </svg>
                            </button>
                          )}
                          <span className="text-white truncate">
                            {t('audio')} ({isPlaying === 'outro' ? (audioDuration - playbackTime) : outroAudioDuration}s)
                          </span>
                        </>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 z-10">
                      <button
                        type="button"
                        disabled={!isOutroEditing}
                        className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full ${isOutroEditing ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-zinc-700 text-zinc-500 cursor-not-allowed'}`}
                        onClick={(e) => {
                          if (!isOutroEditing) return;
                          e.stopPropagation();
                          if (isPlaying === 'outro') {
                            stopPlayback();
                          }
                          // Show confirmation dialog instead of deleting immediately
                          confirmDeleteAudio('outro');
                        }}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}

                {/* Saving indicator overlay */}
                {isOutroSaving && (
                  <div className="absolute inset-0 border border-white/20 flex items-center justify-center z-20 rounded-lg" style={{
                    boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                  }}>
                    <div className="flex items-center space-x-3">
                      <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-white font-medium">{t('saving')}</span>
                    </div>
                  </div>
                )}

                {/* Audio playback UI */}


                {/* Microphone button */}
                <button
                  type="button"
                  disabled={!isOutroEditing}
                  className={`absolute right-2 p-1.5 rounded-full transition-colors ${isOutroEditing ? 'bg-black/40 text-white/70 hover:bg-jade-purple/30 hover:text-white border border-white/20' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'}`}
                  onClick={(e) => {
                    if (!isOutroEditing) return;
                    e.stopPropagation();
                    e.preventDefault();

                    setShowRecordingPopup(true);
                    setRecordingFor('outro');
                    // Don't start recording here - wait for user to click Start Recording

                    return false;
                  }}
                  title={isOutroEditing ? "Record outro" : "Edit mode required"}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Recording Popup Window */}
          {showRecordingPopup && (
            <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50" onClick={(e) => {
              e.stopPropagation();
            }}>
              <div className="bg-white/5 border border-white/30 rounded-xl p-6 w-full max-w-md mx-4 relative" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
                {/* Close button (X) */}
                <button
                  className="absolute top-3 right-3 p-1 rounded-full bg-black/40 hover:bg-black/60 text-white/60 hover:text-white border border-white/20 transition-colors"
                  onClick={() => {
                    // Make sure to stop recording properly to ensure all tracks are stopped
                    if (isRecording) {
                      stopRecording();
                    } else {
                      // Even if not recording, make sure to clean up any active tracks
                      if (audioStream) {
                        audioStream.getTracks().forEach(track => track.stop());
                        setAudioStream(null);
                      }
                      // Clear recording state and close popup
                      setRecordingFor(null);
                      setShowRecordingPopup(false);
                    }
                  }}
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {recordingFor === 'intro' ? t('record_intro_audio') : t('record_outro_audio')}
                </h3>

                <div className="flex justify-center items-center mb-6 h-32">
                  <div className={`w-32 h-32 rounded-full flex items-center justify-center ${isRecording ? 'bg-red-500/20 border-2 border-red-500 animate-pulse' : 'bg-black/30 border-2 border-white/20'}`} style={{
                    boxShadow: isRecording ? '0 0 15px rgba(239, 68, 68, 0.3)' : 'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  }}>
                    <div className="text-center">
                      <div className="text-2xl font-mono">
                        {isRecording ? formatRecordingTime(recordingTime) : "0s"}
                      </div>
                      <div className="text-sm text-white/60 mt-1">
                        {isRecording ? t('recording') : t('ready')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center">
                  {!isRecording ? (
                    <button
                      onClick={() => {
                        startRecording(recordingFor!);
                      }}
                      className="py-2 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors flex items-center justify-center border-2 border-jade-purple"
                      style={{
                        boxShadow: '0 0 15px rgba(134, 107, 255, 0.3)'
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                      {t('start_recording')}
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        stopRecording();
                      }}
                      className="py-2 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center border-2 border-red-500"
                      style={{
                        boxShadow: '0 0 15px rgba(239, 68, 68, 0.3)'
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                      </svg>
                      {t('stop_recording')}
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Text Editing Popup */}
          {editingItem && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
              onClick={() => {
                // Save and exit when clicking anywhere on the backdrop
                handleTextChange(editingItem.section!, editingItem.value);
                setEditingItem(null);
                setIsUpdating(true);
                // Simulate saving delay
                setTimeout(() => setIsUpdating(false), 500);
              }}
            >
              <div
                className="bg-jade-purple/10 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4 relative"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
                onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from triggering the backdrop click
              >
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {editingItem.section === 'intro' ? t('edit_intro_message') : t('edit_outro_message')}
                </h3>

                <div className="mb-6">
                  <textarea
                    ref={textareaRef}
                    value={editingItem.value}
                    onChange={(e) => {
                      setEditingItem({
                        ...editingItem,
                        value: e.target.value
                      });
                    }}
                    placeholder={editingItem.section === 'intro' ? t('enter_welcome_message') : t('enter_closing_message')}
                    className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:border-white/40 min-h-[120px]"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                  />
                </div>

                <button
                  onClick={() => {
                    handleTextChange(editingItem.section!, editingItem.value);
                    setEditingItem(null);
                    setIsUpdating(true);
                    // Simulate saving delay
                    setTimeout(() => setIsUpdating(false), 500);
                  }}
                  className="py-2 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors w-full border-2 border-jade-purple"
                  style={{
                    boxShadow: '0 0 15px rgba(134, 107, 255, 0.3)'
                  }}
                >
                  {t('done')}
                </button>
              </div>
            </div>
          )}

          {/* Save Confirmation Modal */}
          {showSaveConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
              onClick={() => {
                if (saveStatus === 'idle') {
                  setShowSaveConfirmation(false);
                }
              }}
            >
              <div
                className="bg-jade-purple/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4" style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
                onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
              >
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {confirmationSection === 'intro' ? t('save_intro_message') : t('save_outro_message')}
                </h3>

                <p className="text-white/80 mb-6 text-center">
                  {confirmationSection === 'intro' ? t('save_intro_confirmation') : t('save_outro_confirmation')}
                </p>

                <div className="flex justify-between w-full space-x-4">
                  <button
                    onClick={() => setShowSaveConfirmation(false)}
                    className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                    disabled={saveStatus !== 'idle'}
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={() => {
                      handleSave(confirmationSection!);
                    }}
                    className="flex-1 px-4 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors border-2 border-jade-purple"
                    style={{
                      boxShadow: '0 0 15px rgba(134, 107, 255, 0.3)'
                    }}
                    disabled={saveStatus !== 'idle'}
                  >
                    {t('save')}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Cancel Confirmation Modal */}
          {showCancelConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
              onClick={() => setShowCancelConfirmation(false)}
            >
              <div
                className="bg-red-500/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4" style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
                onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
              >
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {t('discard_changes')}
                </h3>

                <p className="text-white/80 mb-6 text-center">
                  {confirmationSection === 'intro' ? t('unsaved_intro_changes') : t('unsaved_outro_changes')}
                </p>

                <div className="flex justify-between w-full space-x-4">
                  <button
                    onClick={() => setShowCancelConfirmation(false)}
                    className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                  >
                    {t('keep_editing')}
                  </button>
                  <button
                    onClick={confirmCancel}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"
                    style={{
                      boxShadow: '0 0 15px rgba(239, 68, 68, 0.3)'
                    }}
                  >
                    {t('discard')}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Audio Delete Confirmation Modal */}
          {showDeleteAudioConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
              onClick={() => setShowDeleteAudioConfirmation(false)}
            >
              <div
                className="bg-red-500/25 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-md mx-4" style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}
                onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
              >
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {t('delete_audio')}
                </h3>

                <p className="text-white/80 mb-6 text-center">
                  {audioToDelete === 'intro' ? t('delete_intro_audio_confirmation') : t('delete_outro_audio_confirmation')}
                </p>

                <div className="flex justify-between w-full space-x-4">
                  <button
                    onClick={() => setShowDeleteAudioConfirmation(false)}
                    className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={deleteAudio}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"
                    style={{
                      boxShadow: '0 0 15px rgba(239, 68, 68, 0.3)'
                    }}
                  >
                    {t('delete')}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Audio Error Popup - similar to the one in Knowledge page */}
          {showAudioErrorPopup && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="bg-white/5 backdrop-blur-sm border border-red-500/30 rounded-xl p-6 w-full max-w-md mx-4 relative"
                style={{
                  boxShadow: '0 0 15px rgba(239, 68, 68, 0.3), inset 0 0 20px rgba(239, 68, 68, 0.15)'
                }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* Close button */}
                <button
                  onClick={handleCloseAudioErrorPopup}
                  className="absolute top-3 right-3 text-white/60 hover:text-white"
                >
                  <FaTimes className="w-5 h-5" />
                </button>

                {/* Error icon */}
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-red-500/20">
                  <FaExclamationTriangle className="w-6 h-6 text-red-500" />
                </div>

                {/* Error title */}
                <h3 className="text-xl font-bold mb-4 font-title text-center text-white">
                  Audio Recording Error
                </h3>

                {/* Error message */}
                <p className="text-white/80 mb-6 text-center">
                  Audio recordings are only allowed to be under 60 seconds in length.
                </p>
              </div>
            </div>
          )}

          {/* Save Status Overlay - Separate from confirmation modal */}
          {saveStatus !== 'idle' && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
              onClick={() => {
                if (saveStatus === 'success' || saveStatus === 'error') {
                  setSaveStatus('idle');
                }
              }}
            >
              <div
                className={`p-6 rounded-xl max-w-md w-full mx-4 ${
                  saveStatus === 'loading' ? 'bg-jade-purple/30 border border-jade-purple/50 text-white' :
                  saveStatus === 'success' ? 'bg-green-500/20 border border-green-500/30 text-green-400' :
                  'bg-red-500/20 border border-red-500/30 text-red-400'
                } backdrop-blur-md`}
                onClick={(e) => e.stopPropagation()}
              >
                {saveStatus === 'loading' ? (
                  <div className="flex flex-col items-center text-center">
                    <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-lg font-semibold mb-3">Saving {confirmationSection === 'intro' ? 'Intro' : 'Outro'} Message...</p>
                    <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                      <div
                        className="bg-white h-3 rounded-full transition-all duration-300"
                        style={{ width: `${updateProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm">{updateProgress}% complete</p>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className={saveStatus === 'success' ? 'text-green-400' : 'text-red-400'}>
                      {saveStatus === 'success' ? (
                        <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </div>
                    <p className="text-lg font-semibold mb-1">{saveStatus === 'success' ? 'Success!' : 'Error'}</p>
                    <p>{updateMessage}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Image Gallery Modal */}
          {imageGallery && (
            <div className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50">
              <div ref={imageGalleryRef} className="relative bg-white/5 backdrop-blur-sm border border-white/30 rounded-xl p-6 w-full max-w-3xl mx-4" style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
                {/* Close button in the top-right corner */}
                <button
                  onClick={() => setImageGallery(null)}
                  className="absolute top-3 right-3 p-1.5 rounded-full bg-black/40 hover:bg-black/60 text-white/60 hover:text-white border border-white/20 transition-colors z-20"
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                {/* Navigation buttons - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={showPreviousImage}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Previous image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={showNextImage}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Next image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}

                <div className="flex flex-col">
                  {/* Image counter */}
                  <div className="text-center mb-2 text-sm text-white/60">
                    {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
                  </div>

                  {/* Main image container with touch events */}
                  <div
                    className="w-full flex items-center justify-center h-[60vh]"
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                    aria-live="polite"
                    role="region"
                    aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                  >
                    {imageGallery.urls.length === 0 ? (
                      <div className="flex flex-col items-center justify-center text-white/50">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p>No images available</p>
                      </div>
                    ) : (
                      <div className={`relative ${isZoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'}`}>
                        <img
                          src={imageGallery.urls[imageGallery.currentIndex]}
                          alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                          className={`
                            rounded-lg transition-all duration-300 ease-in-out
                            ${isZoomed ? 'max-w-none max-h-none scale-150' : 'max-w-full max-h-full object-contain'}
                          `}
                          onClick={handleImageClick}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                          }}
                        />
                        {isZoomed && (
                          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black/70 text-white text-xs py-1 px-3 rounded-full">
                            Click to zoom out
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Thumbnail strip - only show if more than one image */}
                  {imageGallery.urls.length > 1 && (
                    <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                      {imageGallery.urls.map((url, index) => (
                        <div
                          key={index}
                          onClick={() => {
                            setImageGallery({
                              ...imageGallery,
                              currentIndex: index
                            });
                            setIsZoomed(false); // Reset zoom state
                          }}
                          className={`w-16 h-16 rounded-lg overflow-hidden cursor-pointer flex-shrink-0 border-2 transition-all ${
                            index === imageGallery.currentIndex ? 'border-white/80 scale-105' : 'border-white/20 opacity-60 hover:opacity-100'
                          }`}
                        >
                          <img
                            src={url}
                            alt={`Thumbnail ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
