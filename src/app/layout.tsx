import './globals.css'
import type { Metadata } from 'next'
import { Inter, Plus_Jakarta_Sans, Dangrek, Noto_Sans_Khmer } from 'next/font/google'
import { AuthProvider } from '@/context/AuthContext'
import { LanguageProvider } from '@/context/LanguageContext'
import { LoadingProvider } from '@/context/LoadingContext'

// Define our fonts
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

const plusJakartaSans = Plus_Jakarta_Sans({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-plus-jakarta-sans',
})

// Add Khmer fonts
const dangrek = Dangrek({
  weight: '400',
  subsets: ['khmer'],
  display: 'swap',
  variable: '--font-khmer-title',
})

const notoSansKhmer = Noto_Sans_Khmer({
  subsets: ['khmer'],
  display: 'swap',
  variable: '--font-khmer-body',
})

export const metadata: Metadata = {
  title: 'ChhlatBot - Intelligent Conversations for Social Media',
  description: 'Automate customer engagement across all your social platforms with ChhlatBot.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="kh" className={`scroll-smooth ${inter.variable} ${plusJakartaSans.variable} ${dangrek.variable} ${notoSansKhmer.variable}`}>
      <body className="antialiased">
        <AuthProvider>
          <LanguageProvider>
            <LoadingProvider>
              {children}
            </LoadingProvider>
          </LanguageProvider>
        </AuthProvider>
      </body>
    </html>
  )
}