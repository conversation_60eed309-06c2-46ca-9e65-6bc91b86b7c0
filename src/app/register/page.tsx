'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { FaChevronLeft, FaChevronRight, FaTelegram } from 'react-icons/fa'
import { nanoid } from 'nanoid'

// Define the sector categories and their options
const sectorCategories = {
  'Retail & Commerce': [
    'Clothing & Fashion Store',
    'Electronics and Phone Shop',
    'Convenience Store',
    'Grocery Store',
    'Cosmetics and Skincare Shop',
    'Jewelry and Accessories Shop',
    'Motorbike Parts Shop',
    'Car Parts Shop',
    'Online Store',
    'Gift Shop',
    'Other (Retail & Commerce)',
  ],
  'Food & Beverage': [
    'Restaurant',
    'Coffee Shop',
    'Food Delivery',
    'Street Food Vendor',
    'Bakery',
    'Dessert Shop',
    'Drink Stall',
    'Other (Food & Beverage)',
  ],
  'Beauty & Wellness': [
    'Salon',
    'Barber Shop',
    'Nail Studio',
    'Eyelash Studio',
    'Spa',
    'Massage Center',
    'Skincare Clinic',
    'Gym',
    'Fitness Studio',
    'Yoga Studio',
    'Other (Beauty & Wellness)',
  ],
  'Services': [
    'Internet Shop',
    'SIM Card Shop',
    'Phone Repair Service',
    'Electronics Repair Service',
    'AC Repair Service',
    'Delivery Service',
    'Courier Service',
    'Print Shop',
    'Copy Shop',
    'Car Wash',
    'Motorbike Wash',
    'Photo Studio',
    'Video Studio',
    'Event Planner',
    'Decoration Service',
    'Freelance Service',
    'Creative Service',
    'Other (Services)',
  ],
  'Real Estate & Construction': [
    'Property Listing Agent',
    'Rental Management',
    'Construction Material Store',
    'Furniture Store',
    'Home Renovation Service',
    'Other (Real Estate & Construction)',
  ],
  'Education & Training': [
    'Language School',
    'Private School',
    'Kindergarten',
    'Online Course Provider',
    'Tutoring Service',
    'Skill Training Center',
    'Other (Education & Training)',
  ],
  'Medical & Health': [
    'Pharmacy',
    'Dental Clinic',
    'General Clinic',
    'Traditional Medicine Shop',
    'Health Supplement Store',
    'Other (Medical & Health)',
  ],
  'Tourism & Hospitality': [
    'Hotel',
    'Guesthouse',
    'Travel Agency',
    'Tour Operator',
    'Airbnb Host',
    'Homestay Provider',
    'Other (Tourism & Hospitality)',
  ],
  'Finance & Legal': [
    'Microfinance Business',
    'Money Transfer Agent',
    'E-Wallet Agent',
    'Accounting Service',
    'Tax Filing Service',
    'Legal Service',
    'Document Processing Service',
    'Other (Finance & Legal)',
  ],
  'Community & Religious': [
    'Church',
    'Temple',
    'NGO',
    'Community Project',
    'Other (Community & Religious)',
  ],
  'Others': [
    'Other (Not Listed)',
  ],
};
export default function RegisterPage() {
  const [step, setStep] = useState(1)
  const [plan, setPlan] = useState<string>('')
  const [username, setUsername] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('')
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isCheckingUsername, setIsCheckingUsername] = useState(false)
  const [registrationCode, setRegistrationCode] = useState<string>('')
  const [isCopied, setIsCopied] = useState(false)
  const [showCodeModal, setShowCodeModal] = useState(false)
  const [countdown, setCountdown] = useState<number>(15 * 60) // 15 minutes in seconds
  const logoRef = useRef<HTMLDivElement>(null)

  // Check for URL parameters on component mount
  useEffect(() => {
    // Get URL parameters
    const params = new URLSearchParams(window.location.search)
    const planParam = params.get('plan')

    // If plan parameter exists, set it and move to step 2
    if (planParam) {
      // Capitalize first letter to ensure consistent format (Intern or Assistant)
      const normalizedPlan = planParam.charAt(0).toUpperCase() + planParam.slice(1).toLowerCase()
      setPlan(normalizedPlan)
      setStep(2)
    }
  }, [])

  // Handle next step
  const handleNext = async () => {
    if (step === 1 && !plan) {
      setError('Please select a plan')
      return
    }

    if (step === 2) {
      if (!username.trim()) {
        setError('Please enter a username')
        return
      }

      // Validate username format (alphanumeric and underscores only)
      const usernameRegex = /^[a-zA-Z0-9_]+$/
      if (!usernameRegex.test(username.trim())) {
        setError('Username can only contain letters, numbers, and underscores')
        return
      }

      // Check if username already exists
      setError(null)
      setIsCheckingUsername(true)

      try {
        const response = await fetch('/api/register/check-username', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: username.trim() }),
        })

        const data = await response.json()

        if (data.exists) {
          setError('This username is already taken. Please choose another one.')
          setIsCheckingUsername(false)
          return
        }

        // If there's a warning, show it but allow the user to proceed
        if (data.warning) {
          // Show warning but don't prevent proceeding
          setError(`Note: ${data.warning}`)
        }
      } catch (err) {
        console.error('Error checking username:', err)
        setError('Failed to check username availability. Please try again.')
        setIsCheckingUsername(false)
        return
      }

      setIsCheckingUsername(false)
    }

    if (step === 3 && (!selectedCategory || !selectedSubcategory)) {
      setError('Please select both a category and subcategory')
      return
    }

    setError(null)
    setStep(step + 1)
  }

  // Handle previous step
  const handlePrevious = () => {
    setError(null)
    setStep(step - 1)
  }

  // Handle category selection
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    setSelectedSubcategory('')
  }

  // Handle registration
  const handleRegister = async () => {
    // Validate subcategory selection
    if (!selectedSubcategory) {
      setError('Please select a subcategory')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Generate a unique registration code
      const code = nanoid(16)
      setRegistrationCode(code)

      // Store registration data in Redis
      const response = await fetch('/api/register/store-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          plan,
          username: username.trim(),
          sector: selectedSubcategory
        }),
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error('Failed to store registration data')
      }

      // Reset countdown and show the registration code modal
      setCountdown(15 * 60) // Reset to 15 minutes
      setShowCodeModal(true)
    } catch (err: any) {
      setError(err.message || 'An error occurred during registration')
    } finally {
      setIsLoading(false)
    }
  }

  // Format time from seconds to MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Start countdown when code modal is shown
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (showCodeModal && countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prevCountdown => {
          if (prevCountdown <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prevCountdown - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [showCodeModal, countdown]);

  // Handle copy code to clipboard
  const handleCopyCode = () => {
    if (registrationCode) {
      navigator.clipboard.writeText(registrationCode)
        .then(() => {
          setIsCopied(true)
          setTimeout(() => setIsCopied(false), 2000)
        })
        .catch(err => {
          console.error('Failed to copy code:', err)
        })
    }
  }
  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative">
      <div ref={logoRef} className="px-4 py-8 mx-auto flex items-center justify-center relative z-[60]">
        <Link href="/">
          <img
            src="/images/white_tran_logo.svg"
            alt="ChhlatBot"
            className="h-10 w-auto"
          />
        </Link>
      </div>

      <div className="flex-grow flex items-center justify-center px-4 py-8">
        <motion.div
          className="w-full max-w-md p-8 rounded-xl shadow-xl"
          style={{
            border: '1px solid rgba(134, 107, 255, 0.5)',
            backgroundColor: 'rgba(255, 255, 255, 0.025)',
            boxShadow: '0 0 15px rgba(134, 107, 255, 0.5)'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl sm:text-3xl font-bold mb-6 font-title text-center">
            Register for ChhlatBot
          </h1>

          {/* Step indicator */}
          <div className="flex justify-between mb-8">
            {[1, 2, 3].map((stepNumber) => (
              <div
                key={stepNumber}
                className={`w-8 h-8 rounded-full flex items-center justify-center border ${
                  step === stepNumber
                    ? 'bg-jade-purple/20 text-white border-jade-purple'
                    : step > stepNumber
                      ? 'bg-jade-purple/20 text-white border-jade-purple'
                      : 'bg-black/30 text-zinc-400 border-white/20'
                }`}
                style={{
                  boxShadow: step >= stepNumber
                    ? '0 0 10px rgba(134, 107, 255, 0.3)'
                    : 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
              >
                {stepNumber}
              </div>
            ))}
          </div>

          {/* Form steps */}
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-4 text-zinc-300">
                  <p>Select your plan</p>
                  <p className="text-sm text-zinc-500 mt-1">(All new users get a 1-month free trial)</p>
                </div>

                <div className="space-y-4">
                  <button
                    type="button"
                    onClick={() => setPlan('Intern')}
                    className={`w-full p-4 rounded-lg border transition-colors ${
                      plan === 'Intern' || plan === 'intern'
                        ? 'border-jade-purple bg-jade-purple/20 text-white'
                        : 'border-white/25 bg-white/5 hover:bg-transparent'
                    }`}
                  >
                    <div className="font-extrabold text-2xl mb-3">Intern</div>
                    <div className="text-center space-y-1 text-sm py-2 px-3 rounded-lg mt-2">
                      <div>1,000 messages</div>
                      <div>2 channels</div>
                      <div>Text support only</div>
                    </div>
                  </button>

                  <button
                    type="button"
                    onClick={() => setPlan('Assistant')}
                    className={`w-full p-4 rounded-lg border transition-colors ${
                      plan === 'Assistant' || plan === 'assistant'
                        ? 'border-jade-purple bg-jade-purple/20 text-white'
                        : 'border-white/25 bg-white/5 hover:bg-transparent'
                    }`}
                  >
                    <div className="font-extrabold text-2xl mb-3">Assistant</div>
                    <div className="text-center space-y-1 text-sm py-2 px-3 rounded-lg mt-2">
                      <div>2,000 messages</div>
                      <div>3 channels</div>
                      <div>Text & Voice support</div>
                    </div>
                  </button>
                </div>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-4 text-zinc-300">
                  <p>Choose a username for your account</p>
                  <p className="text-sm text-zinc-500 mt-1">(This will be your unique identifier in the system)</p>
                </div>

                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-zinc-300 mb-2">
                    Username
                  </label>
                  <div className="relative">
                    <input
                      id="username"
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="w-full px-4 py-3 rounded-lg bg-black/30 border border-white/20 text-white placeholder-zinc-500 focus:outline-none focus:border-white/40"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                      placeholder="Enter your username"
                      required
                      disabled={isCheckingUsername}
                    />
                    {isCheckingUsername && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-jade-purple"></div>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-zinc-500 mt-1">
                    Username can only contain letters, numbers, and underscores
                  </p>
                </div>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className="text-center mb-4 text-zinc-300">
                  <p>Select your business sector</p>
                  <p className="text-sm text-zinc-500 mt-1">(This helps us customize ChhlatBot for your specific industry)</p>
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-zinc-300 mb-2">
                    Category
                  </label>
                  <select
                    id="category"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                    className="w-full px-4 py-3 rounded-lg bg-black/30 border border-white/20 text-white focus:outline-none focus:border-white/40"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                  >
                    <option value="">Select a category</option>
                    {Object.keys(sectorCategories).map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                {selectedCategory && (
                  <div>
                    <label htmlFor="subcategory" className="block text-sm font-medium text-zinc-300 mb-2">
                      Subcategory
                    </label>
                    <select
                      id="subcategory"
                      value={selectedSubcategory}
                      onChange={(e) => setSelectedSubcategory(e.target.value)}
                      className="w-full px-4 py-3 rounded-lg bg-black/30 border border-white/20 text-white focus:outline-none focus:border-white/40"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}
                    >
                      <option value="">Select a subcategory</option>
                      {sectorCategories[selectedCategory as keyof typeof sectorCategories].map((subcategory) => (
                        <option key={subcategory} value={subcategory}>
                          {subcategory}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {error && (
            <div className="p-3 mt-6 rounded-lg bg-red-500/10 border border-red-500/20 text-red-500 text-sm">
              {error}
            </div>
          )}

          <div className="mt-8 flex justify-between">
            {step > 1 ? (
              <button
                type="button"
                onClick={handlePrevious}
                className="flex items-center px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white font-medium rounded-lg transition-colors"
                style={{
                  boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                }}
              >
                <FaChevronLeft className="mr-2" /> Back
              </button>
            ) : (
              <div></div> // Empty div to maintain layout
            )}

            {step < 3 ? (
              <button
                type="button"
                onClick={handleNext}
                disabled={isCheckingUsername}
                className={`flex items-center px-4 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-jade-purple/50 ${step === 1 ? 'mx-auto' : ''}`}
                style={{
                  boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                }}
              >
                {step === 2 && isCheckingUsername ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Checking...
                  </>
                ) : (
                  <>
                    Next <FaChevronRight className="ml-2" />
                  </>
                )}
              </button>
            ) : (
              <button
                type="button"
                onClick={handleRegister}
                disabled={isLoading}
                className="flex items-center px-4 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-jade-purple/50"
                style={{
                  boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                }}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Registering...
                  </>
                ) : (
                  'Register'
                )}
              </button>
            )}
          </div>

          <div className="mt-16 text-center text-lg text-zinc-500">
            Already have an account?{' '}
            <Link href="/access" className="text-jade-purple hover:text-jade-purple-light font-medium">
              Log in
            </Link>
          </div>
        </motion.div>
      </div>

      {/* Registration Code Modal */}
      {showCodeModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-black/80 border border-jade-purple/50 rounded-xl p-6 max-w-md w-full relative"
               style={{
                 boxShadow: '0 0 20px rgba(134, 107, 255, 0.3), inset 0 0 20px rgba(134, 107, 255, 0.2)'
               }}>
            <div className="flex items-center justify-center mb-4">
              <div className="bg-jade-purple/20 rounded-full p-3">
                <svg className="w-8 h-8 text-jade-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-white font-title">Registration Complete!</h3>
              <p className="text-sm text-zinc-400 mt-1 font-body">Follow the steps below to activate your account</p>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-sm text-zinc-300 mb-2">Your registration code:</p>
                <div className="relative">
                  <div className="flex items-center">
                    <div className="w-full bg-black/30 border border-white/20 rounded-lg px-4 pr-12 py-3 font-mono text-white text-center"
                      style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                      }}>
                      {registrationCode}
                    </div>
                    <div className="absolute right-4 flex gap-2">
                      <button
                        type="button"
                        onClick={handleCopyCode}
                        className="text-zinc-400 hover:text-white"
                        title="Copy to clipboard"
                      >
                        {isCopied ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
                {isCopied && (
                  <p className="text-xs text-green-500 mt-1 text-center">Copied to clipboard!</p>
                )}
              </div>

              <div className="text-center">
                <div className="text-sm text-zinc-400">
                  <p>This code will expire in</p>
                  <div className="text-jade-purple font-medium text-3xl mb-2">{formatTime(countdown)}</div>
                  <p className="mt-1">Please copy this code and send it to our Telegram bot to complete your registration.</p>
                  <br></br>
                  <p className="mt-1">Please note: The Telegram account you use to send this code will be the one we use for all future notifications and important updates from our service.
Make sure to use the Telegram account that you primarily intend to manage and receive communications from us.</p>
                </div>
              </div>

              <a
                href="https://t.me/chhlatbot_support_bot"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-full py-3 px-4 bg-[#0088cc] hover:bg-[#0077b5] text-white font-medium rounded-lg transition-colors font-body"
                style={{
                  boxShadow: '0 0 10px rgba(0, 136, 204, 0.3)'
                }}
              >
                <FaTelegram className="mr-2" /> Open Telegram
              </a>

              <div className="text-center text-sm text-zinc-400 mt-2">
                <p>After sending the code to our Telegram bot, you will receive your login credentials.</p>
              </div>

              <div className="mt-4 text-center">
                <Link
                  href="/access"
                  className="inline-block px-6 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white font-medium rounded-lg transition-colors border border-jade-purple/50"
                  style={{
                    boxShadow: '0 0 10px rgba(134, 107, 255, 0.3)'
                  }}
                >
                  Log in
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
