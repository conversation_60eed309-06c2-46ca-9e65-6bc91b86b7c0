'use client'

import { motion } from 'framer-motion'
import TerminalAnimation from './TerminalAnimation'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import Link from 'next/link'
import GlassButton from './ui/GlassButton'

export default function Hero() {
  const { user } = useAuth()
  const { t } = useLanguage()

  return (
    <section className="section flex flex-col items-center justify-center min-h-screen py-8 pt-52 sm:pt-36 md:pt-24 relative overflow-hidden">
      {/* Grid pattern */}
      {/* <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
          ))}
        </div>
      </div> */}

      <motion.div
        className="w-full text-center mb-12 relative z-10 mt-16 sm:mt-12 md:mt-0"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 md:mb-8 font-title" dangerouslySetInnerHTML={{ __html: t('hero_title') }}>
        </h1>
        <p className="text-sm sm:text-lg md:text-xl mb-10 font-body opacity-80 max-w-3xl mx-auto">
          {t('hero_subtitle')}
        </p>

        {user ? (
          <Link href="/dashboard">
            <GlassButton className="text-lg" variant="filled">
              {t('go_to_dashboard')}
            </GlassButton>
          </Link>
        ) : (
          <Link href="/register">
            <GlassButton className="text-lg" variant="filled">
              {t('try_free')}
            </GlassButton>
          </Link>
        )}
      </motion.div>

      <motion.div
        className="w-full max-w-2xl mx-auto relative z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <TerminalAnimation />
      </motion.div>
    </section>
  )
}
