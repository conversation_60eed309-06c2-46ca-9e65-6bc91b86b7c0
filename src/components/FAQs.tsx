'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useLanguage } from '@/context/LanguageContext'

type FAQItem = {
  question: string;
  answer: string;
}

export default function FAQs() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  const { t } = useLanguage()

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  const faqs: FAQItem[] = [
    {
      question: t('faq1_question'),
      answer: t('faq1_answer')
    },
    {
      question: t('faq2_question'),
      answer: t('faq2_answer')
    },
    {
      question: t('faq3_question'),
      answer: t('faq3_answer')
    },
    {
      question: t('faq4_question'),
      answer: t('faq4_answer')
    },
    {
      question: t('faq5_question'),
      answer: t('faq5_answer')
    },
    {
      question: t('faq6_question'),
      answer: t('faq6_answer')
    },
    {
      question: t('faq7_question'),
      answer: t('faq7_answer')
    }
  ];

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  }

  return (
    <section className="py-24 relative overflow-hidden" ref={ref}>
      {/* Grid pattern */}
      {/* <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
          ))}
        </div>
      </div> */}

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: 20 }}
        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 font-title" dangerouslySetInnerHTML={{ __html: t('faq_title') }}>
        </h2>
        <p className="max-w-2xl mx-auto text-base sm:text-lg font-body">
          {t('faq_subtitle')}
        </p>
      </motion.div>

      <motion.div
        className="max-w-3xl mx-auto"
        variants={container}
        initial="hidden"
        animate={inView ? "show" : "hidden"}
      >
        {faqs.map((faq, index) => (
          <motion.div
            key={index}
            variants={item}
            className="mb-6 relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-xl rounded-2xl border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
            style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
          >
            {/* Subtle background gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>

            <button
              className="w-full text-left p-6 flex justify-between items-center font-body font-medium text-white relative z-10 hover:text-jade-purple transition-colors duration-300"
              onClick={() => toggleFAQ(index)}
            >
              <span className="pr-4">{faq.question}</span>
              <div className={`flex-shrink-0 w-8 h-8 bg-jade-purple/20 rounded-lg flex items-center justify-center border border-jade-purple/30 transform transition-all duration-300 ${openIndex === index ? 'rotate-45 bg-jade-purple/40' : 'rotate-0'}`}
                style={{
                  boxShadow: '0 4px 16px rgba(134, 107, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                  <path d="M12 4V20M4 12H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </button>

            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: openIndex === index ? 'auto' : 0,
                opacity: openIndex === index ? 1 : 0
              }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden relative z-10"
            >
              <div className="px-6 pb-6 text-zinc-300 font-body border-t border-white/10 pt-4 leading-relaxed">
                {faq.answer}
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        className="text-center mt-10"
        initial={{ opacity: 0 }}
        animate={inView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ delay: 0.6 }}
      >
        <p className="text-gray-400 font-body">
          {t('faq_more_questions')} <a href="#" className="text-jade-purple hover:underline">{t('faq_contact_support')}</a>
        </p>
      </motion.div>
      </div>
    </section>
  )
}