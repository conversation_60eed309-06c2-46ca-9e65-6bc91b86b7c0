import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  try {
    // Create a response object to modify
    const response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    })
    
    // Create supabase client with cookies from request
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            // Setting cookies on the response
            response.cookies.set({
              name,
              value,
              ...options,
            })
          },
          remove(name: string, options: CookieOptions) {
            // Removing cookies from the response
            response.cookies.set({
              name,
              value: '',
              ...options,
              maxAge: 0,
            })
          },
        },
      }
    )
    
    // Refresh session if expired or check if it's valid
    try {
      const { data, error } = await supabase.auth.getUser()
      
      // Remove the dashboard bypass code
      
      if (error && request.nextUrl.pathname !== '/login' && request.nextUrl.pathname !== '/signup' && !request.nextUrl.pathname.startsWith('/auth/')) {
        // If auth error and user is trying to access protected routes, clear invalid cookies
        const allCookies = request.cookies.getAll()
        for (const cookie of allCookies) {
          if (cookie.name.includes('sb-') || cookie.name.includes('-auth-token')) {
            response.cookies.set({
              name: cookie.name,
              value: '',
              maxAge: 0,
            })
          }
        }
      }
    } catch (authError) {
      console.error('Auth error in middleware:', authError)
    }
    
    return response
  } catch (error) {
    console.error('Error in updateSession:', error)
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    })
  }
} 
