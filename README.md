# ChhlatBot Landing Page

A modern, developer-focused landing page for ChhlatBot, a SaaS AI chatbot provider for social media business owners.

## Tech Stack

- **Next.js** - React framework for server-side rendering and static site generation
- **TypeScript** - For type safety and better developer experience
- **Tailwind CSS** - Utility-first CSS framework for styling
- **Framer Motion** - For smooth animations and transitions

## Features

- Responsive design that works on all devices
- Modern, developer-focused aesthetic with clean lines and high contrast
- Interactive elements with subtle animations
- Code-inspired design elements for technical audience

## Getting Started

Since this project requires Node.js and npm, you'll need to install them first. Once installed, you can run:

```bash
# Install dependencies
npm install

# Run the development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

- `/src/app` - Next.js app router pages
- `/src/components` - Reusable React components
- `/src/styles` - Global styles and CSS modules
- `/public` - Static assets like images and icons

## Deployment

This project can be deployed on Vercel or any other hosting platform that supports Next.js.

```bash
# Build for production
npm run build

# Start the production server
npm start
```

## License

This project is intended for demonstration purposes. 