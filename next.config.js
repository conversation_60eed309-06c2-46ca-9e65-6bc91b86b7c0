// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   reactStrictMode: true,
//   swcMinify: true,
//   webpack: (config, { isServer }) => {
//     // Add any webpack config modifications if needed for hot reloading
//     config.watchOptions = {
//       ...config.watchOptions,
//       poll: 1000,
//       aggregateTimeout: 300,
//     };
    
//     return config;
//   },
// }

// module.exports = nextConfig



/** @type {import('next').NextConfig} */
const isProd = process.env.NODE_ENV === 'production';

const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,

  // Conditionally apply eslint/typescript ignore rules in production
  eslint: {
    ignoreDuringBuilds: isProd,
  },
  typescript: {
    ignoreBuildErrors: isProd,
  },

  // Modify Webpack config
  webpack: (config, { isServer }) => {
    if (!isProd) {
      // Only enable polling in development (e.g., for Docker on Mac/Windows)
      config.watchOptions = {
        ...config.watchOptions,
        poll: 1000,
        aggregateTimeout: 300,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
